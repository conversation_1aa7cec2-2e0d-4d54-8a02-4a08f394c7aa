{
	"easycom": {
		"^tn-(.*)": "@/tuniao-ui/components/tn-$1/tn-$1.vue"
	},
	"pages": [{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				// #ifdef H5
				"titleNView": false,
				// #endif
				"navigationStyle": "default",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/basic/basic",
			// #ifdef H5
			"titleNView": false,
			// #endif
			"style": {
				"navigationBarTitleText": "消息"
			}
		}
	],
	"subPackages": [{
			"root": "subPages",
			"pages": [{
					"path": "dyParse/index",
					"style": {
						"navigationBarTitleText": "抖音去水印",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ksParse/index",
					"style": {
						"navigationBarTitleText": "快手去水印",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "weather/index",
					"style": {
						"navigationBarTitleText": "天气查询",
						"enablePullDownRefresh": false,
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#F8F8F8",
						"backgroundColor": "#F8F8F8"
					}
				},
				{
					"path": "about/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "关于我们",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "bookAudio/index",
					"style": {
						"navigationBarTitleText": "英语音频",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "bookAudio/lesson",
					"style": {
						"navigationBarTitleText": "英语音频",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ttsAudio/index",
					"style": {
						"navigationBarTitleText": "文字转语音",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "invoiceCheck/index",
					"style": {
						"navigationBarTitleText": "发票查验",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mbtiTest/index",
					"style": {
						"navigationBarTitleText": "MBTI测试",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mbtiTest/answer",
					"style": {
						"navigationBarTitleText": "MBTI测试",
						"enablePullDownRefresh": false
					}
				},	
				{
					"path": "mbtiTest/analyze",
					"style": {
						"navigationBarTitleText": "MBTI测试分析",
						"enablePullDownRefresh": false
					}
				}						
			]
		}
	],

	"globalStyle": {
		"navigationStyle": "default",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"condition": {
		//模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}