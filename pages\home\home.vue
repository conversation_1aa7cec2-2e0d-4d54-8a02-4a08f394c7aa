<template>
  <view class="basic tn-safe-area-inset-bottom">
<!-- 	
	<view class="">
		让生活更加简单！
	</view> -->
	<view class="banner-top" >
	  <tn-swiper :list="banner" :height="350" :effect3d="true" mode="round"></tn-swiper>
	</view>
	<!-- 数据信息 -->
	<view class="tn-info__container tn-flex tn-flex-wrap tn-flex-col-center tn-flex-row-between tn-margin">
	  <block v-for="(item, index) in tuniaoData" :key="index" >
	    <view class="tn-info__item tn-flex tn-flex-direction-row tn-flex-col-center tn-flex-row-between job-shadow" @tap="navigateTo(item.path)">
	      <view class="tn-info__item__left tn-flex tn-flex-direction-row tn-flex-col-center tn-flex-row-left">
	        <view class="tn-info__item__left--icon tn-flex tn-flex-col-center tn-flex-row-center tn-color-white" :class="[`tn-bg-${item.color}`]">
	          <view :class="[`tn-icon-${item.icon}`]"></view>
	        </view>
	        <view class="tn-info__item__left__content">
	          <view class="tn-info__item__left__content--title">{{ item.title }}</view>
	          <view class="tn-info__item__left__content--data tn-padding-top-xs">{{ item.value }}</view>
	        </view>
	      </view>
	      <view class="tn-info__item__right">
	        <view class="tn-info__item__right--icon">
	          <view class="tn-icon-right"></view>
	        </view>
	      </view>
	    </view>
	  </block>
	</view>
	
	<!-- 方式16 start-->
	<view class="tn-flex tn-flex-wrap tn-margin job-shadow">
	  <block v-for="(item, index) in icons" :key="index">
	    <view class=" " style="width: 25%;" @tap="navigateTo(item.path)" >
	      <view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center  tn-padding-xl">
	        <view class="icon16__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
	          <view class="tn-cool-color-icon16" :class="[$tn.color.getRandomCoolBgClass(index) + ' tn-icon-' + item.icon]"></view>
	        </view>  
	        <view class="tn-color-black tn-text-lg tn-text-center">
	          <text class="tn-text-ellipsis">{{item.title}}</text>
	        </view>
	      </view>
	    </view>
	  </block>
	</view>
	<!-- 方式16 end-->
  </view>
</template>

<script>

  export default {
    name: 'Home',
    filters: {
      titleFilter(value) {
        if (value.length === 0) {
          return ''
        }
        let newString = ''
        for (let i = 0; i < value.length; i++) {
          if (i !== 0) {
            newString += ' / '
          }
          newString += value[i]
        }
        return newString
      }
    },
    data() {
      return {
        // nav菜单列表
		  banner: [
			{image: 'https://auth.bogerj.cn/wximg/pic01.jpg', title: 'xxx'},
			{image: 'https://auth.bogerj.cn/wximg/pic02.jpg', title: 'xxx'},
			{image: 'https://auth.bogerj.cn/wximg/pic03.jpg', title: 'xxx'},
			{image: 'https://auth.bogerj.cn/wximg/pic04.jpg', title: 'xxx'},
		  ],
        tuniaoData: [
          {
            title: '抖音水印',
            icon: 'tiktok',
            color: 'purplered',
            value: '32',
			path:"/subPages/dyParse/index",
          },
          {
            title: '快手水印',
            icon: 'clear-fill',
            color: 'green',
            value: '65',
			path:"/subPages/ksParse/index",
          },
   //        {
   //          title: '证件抠图',
   //          icon: 'honor',
   //          color: 'indigo',
   //          value: '6',
			// path:"",
   //        }
        ],
        icons: [
          {
            icon: "tiktok",
            title: "抖音水印",
			path:"/subPages/dyParse/index",
          },
          {
            icon: "clear-fill",
            title: "快手水印",
			path:"/subPages/ksParse/index",
          },
		 // {
		 //   icon: "honor",
		 //   title: "证件抠图",
		 //   path:"",
		 // }, 
   //        {
   //          icon: "topics",
   //          title: "送祝福",
			// path:"",
   //        },
   //        {
   //          icon: "constellation",
   //          title: "查星座",
			// path:"",
   //        },
          {
            icon: "wea-sun",
            title: "查天气",	
			path:"/subPages/weather/index",
          },
		  {
		    icon: "english",
		    title: "英语音频",
		  	path:"/subPages/bookAudio/index",
		  },
          {
            icon: "circle-lack",
            title: "文字转语音",	
			path:"/subPages/ttsAudio/index",
          },
		  {
			title: '发票验重',
			icon: 'topics',
			color: 'orange',
			path:"/subPages/invoiceCheck/index",
		  },
		  {
			title: 'MBTI测试',
			icon: 'test',
			color: 'orange',
			path:"/subPages/mbtiTest/index",
		   },			
		   {
			 icon: "tel",
			 title: "联系我们",	
			 path:"/subPages/about/index",
		   },
        ],				  
      }
    },
    methods: {
      getRandomCoolBg() {
        return this.$tn.color.getRandomCoolBgClass()
      },
      navigateTo(toPath) {
		if (toPath==""){
			uni.showToast({
				title:"功能正在开发中",
				duration:1000
			})
		}else{
			uni.navigateTo({
			  url: toPath
			})
		}
      },	  
    }
  }
</script>

<style lang="scss" scoped>
	@import '@/static/css/custom_nav_bar.scss';
	.basic{

	}
	.banner-top{
		padding-top: 30upx;
	}
  /* 信息展示 start */
  .tn-info {
    
    &__container {
      margin-top: 40rpx;
    }
    
    &__item {
      width: 48%;
      margin: 15rpx 0rpx;
      padding: 38rpx 28rpx;
      border-radius: 10rpx;
      
      &__left {
        
        &--icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          font-size: 40rpx;
          margin-right: 20rpx;
          position: relative;
          z-index: 1;
          
          &::after {
            content: " ";
            position: absolute;
            z-index: -1;
            width: 100%;
            height: 100%;
            left: 0;
            bottom: 0;
            border-radius: inherit;
            opacity: 1;
            transform: scale(1, 1);
            background-size: 100% 100%;
            background-image: url(https://resource.tuniaokj.com/images/cool_bg_image/icon_bg3.png);
          }
        }
        
        &__content {
          font-size: 30rpx;
          
          &--data {
            margin-top: 5rpx;
            font-weight: bold;
          }
        }
      }
      
      &__right {
        &--icon {
          font-size: 30rpx;
          opacity: 0.5;
        }
      }
    }
  }
  .job-shadow{
      box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.07);
      border-radius: 20rpx;
  }
  /* 信息展示 end */
  
  /* 图标容器16 start */
  .tn-cool-color-icon16{
    // background-image: -webkit-linear-gradient(135deg, #ED1C24, #FECE12);   16
    // background-image: linear-gradient(135deg, #ED1C24, #FECE12);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
  }
    .icon16 {
      &__item {
        // width: 30%;
        background-color: #FFFFFF;
        border-radius: 10rpx;
        padding: 0rpx;
        margin: 0rpx;
        transform: scale(1);
        transition: transform 0.3s linear;
        transform-origin: center center;
        
        &--icon {
          width: 100rpx;
          height: 100rpx;
          font-size: 70rpx;
          border-radius: 50%;
          margin-bottom: 18rpx;
          position: relative;
          z-index: 1;
        }
      }
    }
  /* 图标容器16 end */
  	
	.logo-image{
	  width: 65rpx;
	  height: 65rpx;
	  position: relative;
	}
	.logo-pic{
	  background-size: cover;
	  background-repeat:no-repeat;
	  // background-attachment:fixed;
	  background-position:top;
	  border-radius: 50%;
	}
</style>
