{"id": "uni-nav-bar", "displayName": "uni-nav-bar 自定义导航栏", "version": "1.3.15", "description": "自定义导航栏组件，主要用于头部导航。", "keywords": ["uni-ui", "导航", "导航栏", "自定义导航栏"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": "", "uni-app": "^4.22", "uni-app-x": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-scss", "uni-icons"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "√", "qq": "√", "lark": "√"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}