<template>
	<view class="index-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="bg-particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></view>
		</view>

		<!-- 主内容区域 -->
		<view class="main-content">
			<!-- 标题区域 -->
			<view class="title-section">
				<view class="title-icon">🧠</view>
				<view class="main-title">MBTI性格测试</view>
				<view class="subtitle">探索你的内在世界</view>
			</view>

			<!-- 介绍卡片 -->
			<view class="intro-card">
				<view class="intro-header">
					<view class="intro-icon">✨</view>
					<text class="intro-title">开始你的性格探索之旅</text>
				</view>
				<view class="intro-content">
					<text class="intro-text">MBTI是世界上最权威的性格测试工具之一，帮助你深入了解自己的性格特质、优势和发展方向。</text>
				</view>
			</view>

			<!-- 答题提示卡片 -->
			<view class="tips-card">
				<view class="tips-header">
					<view class="tips-icon">💡</view>
					<text class="tips-title">答题指南</text>
				</view>
				<view class="tips-content">
					<view class="tip-item" v-for="(tip, index) in displayedTips" :key="index">
						<view class="tip-number">{{ index + 1 }}</view>
						<text class="tip-text">{{ tip }}</text>
					</view>
				</view>
			</view>

			<!-- 特性展示 -->
			<view class="features-section">
				<view class="feature-item" v-for="(feature, index) in features" :key="index" :style="getFeatureStyle(index)">
					<view class="feature-icon">{{ feature.icon }}</view>
					<text class="feature-title">{{ feature.title }}</text>
					<text class="feature-desc">{{ feature.desc }}</text>
				</view>
			</view>

			<!-- 开始按钮 -->
			<view v-if="showNextFlag" class="start-button-container">
				<view class="start-button" @click="nestStep">
					<view class="button-content">
						<text class="button-text">{{ nextText }}</text>
						<view class="button-icon">🚀</view>
					</view>
					<view class="button-glow"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				topics:[],
				tips:[],
				displayedTips:[],
				currentTipIndex:0,
				charIndex:0,
				showNextFlag:false,
				nextText:"开始测试",
				features: [
					{
						icon: '🎯',
						title: '精准分析',
						desc: '科学的测试方法'
					},
					{
						icon: '🔍',
						title: '深度洞察',
						desc: '全面了解性格特质'
					},
					{
						icon: '📊',
						title: '详细报告',
						desc: '专业的结果解读'
					},
					{
						icon: '💼',
						title: '职业指导',
						desc: '个性化职业建议'
					}
				]
			}
		},
		mounted() {
			// 逐行逐字显示提示
			this.showTipsText();
		},
		methods: {

			nestStep(){
				uni.navigateTo({
					url:"/subPages/mbtiTest/answer"
				})
			},

			// 获取粒子样式
			getParticleStyle(index) {
				const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
				const size = Math.random() * 10 + 5;
				const left = Math.random() * 100;
				const animationDelay = Math.random() * 5;
				const animationDuration = Math.random() * 3 + 2;

				return {
					width: size + 'rpx',
					height: size + 'rpx',
					left: left + '%',
					backgroundColor: colors[index % colors.length],
					animationDelay: animationDelay + 's',
					animationDuration: animationDuration + 's'
				};
			},

			// 获取特性样式
			getFeatureStyle(index) {
				return {
					animationDelay: (index * 0.2) + 's'
				};
			},
			
			// 显示提示文本
			showTipsText(){
				this.tips = [
					"这些问题都没有正确的答案",
					"快速回答问题，不要过度分析",
					"有些似乎措辞不好，选择感觉最好的",
					"以 “真实情况”来回答问题",
					"而不是,你希望被别人看到的方式",
				];
				this.displayNextChar();
			},
			
			// 逐行显示函数
			displayNextChar() {
				// 设置“下一步”按钮的延后显示
				setTimeout(()=>{
					this.showNextFlag = true;
				}, 10000)
				if (this.currentTipIndex < this.tips.length) {
					if (this.charIndex < this.tips[this.currentTipIndex].length) {
						this.displayedTips[this.currentTipIndex] = (this.displayedTips[this.currentTipIndex] || "") + this.tips[this.currentTipIndex][this.charIndex];
						this.charIndex++;
						setTimeout(this.displayNextChar, 100); // 调整速度
					} else {
						this.currentTipIndex++;
						this.charIndex = 0;
						this.displayedTips.push(""); // 添加新行
						setTimeout(this.displayNextChar, 500); // 下一行的间隔
					}
				}
			}
			
		}
	}
</script>

<style scoped>
/* 全局容器 */
.index-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 0;
}

.bg-particle {
	position: absolute;
	border-radius: 50%;
	opacity: 0.6;
	animation: float 5s ease-in-out infinite;
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
	50% { transform: translateY(-30px) rotate(180deg); opacity: 1; }
}

/* 主内容 */
.main-content {
	position: relative;
	z-index: 1;
	padding: 60rpx 40rpx 120rpx;
}

/* 标题区域 */
.title-section {
	text-align: center;
	margin-bottom: 80rpx;
	animation: slideInDown 0.8s ease-out;
}

.title-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	animation: pulse 2s ease-in-out infinite;
}

.main-title {
	font-size: 64rpx;
	font-weight: 900;
	color: white;
	margin-bottom: 20rpx;
	text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
	letter-spacing: 4rpx;
}

.subtitle {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 300;
}

/* 介绍卡片 */
.intro-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 30rpx;
	padding: 50rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 0.6s ease-out;
}

.intro-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.intro-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.intro-text {
	font-size: 30rpx;
	color: #666;
	line-height: 1.8;
	text-align: justify;
}

/* 提示卡片 */
.tips-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 30rpx;
	padding: 50rpx;
	margin-bottom: 60rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 0.8s ease-out;
}

.tips-header {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
}

.tips-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.tips-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.tip-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 30rpx;
	padding: 25rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
	border-radius: 20rpx;
	border-left: 6rpx solid #667eea;
	animation: slideInLeft 0.6s ease-out;
}

.tip-item:last-child {
	margin-bottom: 0;
}

.tip-number {
	width: 50rpx;
	height: 50rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 24rpx;
	margin-right: 25rpx;
	flex-shrink: 0;
}

.tip-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	flex: 1;
}

/* 特性展示 */
.features-section {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	margin-bottom: 80rpx;
}

.feature-item {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 25rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: zoomIn 0.6s ease-out;
	transition: transform 0.3s ease;
}

.feature-item:hover {
	transform: translateY(-10rpx);
}

.feature-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
	display: block;
}

.feature-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}

/* 开始按钮 */
.start-button-container {
	text-align: center;
	animation: slideInUp 1s ease-out;
}

.start-button {
	position: relative;
	display: inline-block;
	background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
	border-radius: 60rpx;
	padding: 30rpx 80rpx;
	box-shadow: 0 20rpx 60rpx rgba(255, 107, 107, 0.4);
	transition: all 0.3s ease;
	overflow: hidden;
}

.start-button:active {
	transform: scale(0.95);
}

.button-content {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
}

.button-text {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	margin-right: 15rpx;
}

.button-icon {
	font-size: 32rpx;
}

.button-glow {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
	border-radius: 60rpx;
	animation: shimmer 2s ease-in-out infinite;
}

/* 动画效果 */
@keyframes slideInDown {
	from {
		opacity: 0;
		transform: translateY(-100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-50rpx);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale(0.8);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.features-section {
		grid-template-columns: 1fr;
	}

	.main-title {
		font-size: 48rpx;
	}

	.intro-card, .tips-card {
		padding: 40rpx;
	}
}
</style>
