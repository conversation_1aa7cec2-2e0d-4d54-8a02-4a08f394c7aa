<template>
	<view>
		<view class="index_container">
			<view class="mbti_title">
				MBTI测试
			</view>
			<!-- 答题提示 -->
			<view class="text_tips">
				<p class="tip" v-for="(tip, index) in displayedTips" :key="index">{{ tip }}</p>
			</view>
			<!-- 下一步按钮 -->
			<view v-if="showNextFlag" class="next_button" @click="nestStep">{{ nextText }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				topics:[],
				tips:[],
				displayedTips:[],
				currentTipIndex:0,
				charIndex:0,
				showNextFlag:false,
				nextText:"立即测试",
			}
		},
		mounted() {
			// 逐行逐字显示提示
			this.showTipsText();
		},
		methods: {
			
			nestStep(){
				uni.navigateTo({
					url:"/subPages/mbtiTest/answer"
				})
			},
			
			// 显示提示文本
			showTipsText(){
				this.tips = [
					"这些问题都没有正确的答案",
					"快速回答问题，不要过度分析",
					"有些似乎措辞不好，选择感觉最好的",
					"以 “真实情况”来回答问题",
					"而不是,你希望被别人看到的方式",
				];
				this.displayNextChar();
			},
			
			// 逐行显示函数
			displayNextChar() {
				// 设置“下一步”按钮的延后显示
				setTimeout(()=>{
					this.showNextFlag = true;
				}, 10000)
				if (this.currentTipIndex < this.tips.length) {
					if (this.charIndex < this.tips[this.currentTipIndex].length) {
						this.displayedTips[this.currentTipIndex] = (this.displayedTips[this.currentTipIndex] || "") + this.tips[this.currentTipIndex][this.charIndex];
						this.charIndex++;
						setTimeout(this.displayNextChar, 100); // 调整速度
					} else {
						this.currentTipIndex++;
						this.charIndex = 0;
						this.displayedTips.push(""); // 添加新行
						setTimeout(this.displayNextChar, 500); // 下一行的间隔
					}
				}
			}
			
		}
	}
</script>

<style  scoped>
.index_container{
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	padding: 0px 30px;
}

.mbti_title{
	text-align: center;
	padding: 10px 0px;
	font-size: 25px;
	margin-top: 25px;
}

.tip{
	padding: 10px 0px;
	text-align: center;
	line-height: 30px;
	margin-top: 10px;
}

.next_button{
	text-align: center;
	border: solid #665e5e 1px;
	width: max-content;
	margin: 0 auto;
	padding: 10px 23px;
	border-radius: 10px;
	background-color: #d36767;
	color: white;
}
</style>
