/**
 * 页面展示列表数据
 */
export default {
  data: [
    {
      title: '基础元素',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'menu-more',
          title: 'Flex布局',
          url: '/basicPage/flex-layout/flex-layout'
        },
        {
          icon: 'menu-circle',
          title: 'Grid布局',
          url: '/basicPage/grid-layout/grid-layout'
        },
        {
          icon: 'gloves',
          title: '配色',
          url: '/basicPage/color/color'
        },
        {
          icon: 'font',
          title: '图标',
          url: '/basicPage/icon/icon'
        },
        {
          icon: 'circle-fill',
          title: '按钮',
          url: '/basicPage/button/button'
        },
        {
          icon: 'tag',
          title: '标签',
          url: '/basicPage/tag/tag'
        },
        {
          icon: 'square',
          title: '边框',
          url: '/basicPage/border/border'
        },
        {
          icon: 'copy-fill',
          title: '阴影',
          url: '/basicPage/shadow/shadow'
        },
        {
          icon: 'moon',
          title: '微标',
          url: '/basicPage/badge/badge'
        },
        {
          icon: 'emoji-good',
          title: '头像',
          url: '/basicPage/avatar/avatar'
        }
      ]
    }, {
      title: '常用工具',
      backgroundColor: 'tn-cool-bg-color-2',
      list: [
        {
          icon: 'code',
          title: 'String工具',
          url: '/basicPage/utils/string/index'
        },
        {
          icon: 'code',
          title: 'Number工具',
          url: '/basicPage/utils/number/index'
        },
        {
          icon: 'code',
          title: 'Message工具',
          url: '/basicPage/utils/message/index'
        },
        {
          icon: 'code',
          title: 'Color工具',
          url: '/basicPage/utils/color/index'
        }
      ]
    }
  ]
}