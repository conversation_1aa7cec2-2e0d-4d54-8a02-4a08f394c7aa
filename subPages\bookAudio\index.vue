<template>
	<view class="basic-dy-parse tn-safe-area-inset-bottom">
		<view class="list-warper">
		  <block v-for="(item, index) in listData" :key="index"  >
			<view class="article-shadow tn-margin" @tap="toLesson(item)">
			  <view class="tn-flex">
				<view class="image-pic tn-margin-sm" :style="'background-image:url(' + item.bookImg + ')'">
				  <view class="image-article">
				  </view>
				</view>
				<view class="tn-margin-sm tn-padding-top-xs" style="width: 100%;">
				  <view class="tn-text-lg tn-text-bold clamp-text-1">
					{{ item.groupName }}
				  </view>
				  <view class="tn-padding-top-xs" style="min-height: 100rpx;">
					<text class=" tn-text-sm tn-color-gray clamp-text-2">
					  {{ item.className }}
					</text>
				  </view>
				  <view class="tn-flex tn-flex-row-between tn-flex-col-between">
					<view
					  class="justify-content-item tn-tag-content__item tn-margin-right tn-round tn-text-sm tn-text-bold">
					  <text class="tn-tag-content__item--prefix">#</text> {{ item.className }}
					</view>
				  </view>
				</view>
			  </view>
			</view>
		  </block>
		</view>
		
	</view>
</template>

<script>
	export default {
		name: 'dyParse',
		components: {},
		data() {
			return {
				dyVidwoUrl: "",
				videoResult: {
					video: "",
					image: "",
					title: ""
				},
				banner: [
					{image: 'https://auth.bogerj.cn/wximg/pic01.jpg', title: 'xxx'},
					{image: 'https://auth.bogerj.cn/wximg/pic02.jpg', title: 'xxx'},
					{image: 'https://auth.bogerj.cn/wximg/pic03.jpg', title: 'xxx'},
					{image: 'https://auth.bogerj.cn/wximg/pic04.jpg', title: 'xxx'},
				],
				listData:[],
				isParse: false,
				downloadShow: false,
				progress: 0,
			}
		},
		onLoad(options) {
			this.getAllClassGroupData()
		},
		methods: {
			getAllClassData(){
				this.$api.getAllClass({},(res)=>{
					
					if (res.code == 200) {
						this.listData = res.data
					} else {
						this.$common.errorToShow(res.msg)
					}
				})
			},
			getAllClassGroupData(){
				this.$api.getClassGroup({},(res)=>{
					
					if (res.code == 200) {
						this.listData = res.data
					} else {
						this.$common.errorToShow(res.msg)
					}
				})
			},	
			toLesson(item){
				uni.navigateTo({
					url:"/subPages/bookAudio/lesson?name="+item.groupName
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.basic-dy-parse {
		background-color: $tn-bg-gray-color;
		height: 100vh;
	}
	.list-warper{
		padding-top: 10upx;
	}
	
  /* 资讯主图 start*/
  .image-article {
    border-radius: 8rpx;
    border: 1rpx solid #F8F7F8;
    width: 200rpx;
    height: 200rpx;
    position: relative;
  }

  .image-pic {
    background-size: cover;
    background-repeat: no-repeat;
    // background-attachment:fixed;
    background-position: top;
    border-radius: 10rpx;
  }

  .article-shadow {
    border-radius: 15rpx;
    box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
  }

  /* 文字截取*/
  .clamp-text-1 {
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .clamp-text-2 {
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }	
</style>