<template>
	<view class="basic-dy-parse tn-safe-area-inset-bottom">
		<!-- 页面内容 -->
		<view class="video-form">
			<view class="video-input">
				<view class="video-input-text">
					<tn-input v-model="text" placeholder="长按粘贴分享信息" :type="'textarea'" :maxHeight="9999" :maxLength='9999'
						:border="true" :clearable="false" :autoHeight="false" />
				</view>
			</view>
			<view class="video-input">
				<view class="video-input-text">
					<tn-radio-group v-model="selVoice" @change="radioGroupChange" width="80">
					  <tn-radio @change="radioChange" v-for="(item, index) in allVoice" :key="index" :name="item.ShortName">
						{{item.Name}}-{{item.Gender}}
					  </tn-radio>
					</tn-radio-group>
				</view>
			</view>	
			<view class="video-input">
				<view class="video-input-text">
					语速 {{rate}}<tn-slider v-model="rate" :mix="1" :max="100" :step="1"></tn-slider>
				</view>
			</view>						
			<view class="bottom-text">
				<tn-button style="margin-right: 10rpx;" :shadow="true" :plain="true" class="btn" height="70rpx"
					@click="parseTxt">解析</tn-button>					
				<tn-button v-if="audioUrl!=''" style="margin-right: 10rpx;" :shadow="true" :plain="true" class="btn" height="70rpx"
					@click="copyText(audioUrl)">复制链接</tn-button>						
				<tn-button :shadow="true" style="margin-right: 10rpx;" :plain="true" class="btn" height="70rpx"
					@click="cleanAll">清空</tn-button>										
			</view>
		</view>
		<view class="audio-warper" v-if="audioUrl!=''" >
			<le-audio
			    :activeIndex="0"
			    :audioData="audioList"
			    :showAudioListIcon="false"
			    :showAudioSpeedIcon="false"
			    :autoplay="false"
			></le-audio>
		</view>		
		<tn-modal v-model="downloadShow" :custom="true" :maskCloseable="false">
			<view class="custom-modal-content">
				<view class="tn-icon tn-icon-about-fill">正在解析中</view>
				<view class="text" style="margin-top: 20rpx;text-align: center;">
					<!-- <tn-line-progress :percent="progress" activeColor="#01BEFF"></tn-line-progress> -->
					<tn-loading mode="circle" color="#e83a30" size='64' :show="true"></tn-loading>
				</view>
			</view>
		</tn-modal>	
	</view>
</template>

<script>	
	export default {
		name: 'ttsAudio',
		components: {},
		data() {
			return {
				videoResult: {
					video: "",
					image: "",
					title: ""
				},
				isParse: false,
				downloadShow: false,
				progress: 0,
				text:"",
				allVoice:[],
				audioUrl:"",
				selVoice:"zh-CN-YunyangNeural",
				rate:30,
				innerAudioContext:null,
				audioList:[],
			}
		},
		onLoad() {
			this.getAllVoice()
			this.innerAudioContext = uni.createInnerAudioContext()
		},
		methods: {
			radioChange(e){
				// console.log(e)
			},
			radioGroupChange(e){
				console.log(e)
			},
			copyText(text) {
				if (text == "") {
					return
				}
				uni.setClipboardData({
					data: text,
					success: function() {
						uni.showToast({
							title: '已复制到剪切板',
							duration: 2000
						})
					}
				})
			},
			copyJson() {
				let jsonStr = JSON.stringify(this.videoResult)
				this.copyText(jsonStr)
			},
			downLoadVideo() {
				this.downloadShow = true
				this.progress = 0
				let that = this
				let downloadUrl=this.audioUrl
				const downloadTask = uni.downloadFile({
					url: downloadUrl, //仅为示例，并非真实的资源
					timeout:1800*1000,
					success: (res) => {
						console.log(res)
						if (res.statusCode === 200) {
							that.saveFile(res,this.videoResult)
							console.log('下载成功');
						} else {
							console.log('下载成功.........');
						}
						that.downloadShow = false
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							title: err.errMsg,
						})
						that.downloadShow = false
					}
				});

				downloadTask.onProgressUpdate((res) => {
					that.progress = res.progress
				});
			},
			saveFile(res,videoInfo) {
				uni.saveVideoToPhotosAlbum({
				  filePath: res.tempFilePath,
				  success:function(){
					uni.hideLoading()
					uni.showToast({title:"保存到相册成功"})
				  },
					fail: function(err) {
						uni.showToast({
							title: '文件保存失败:'+err.errMsg,
							duration: 2000
						})
						console.log(err);
					}				  
				})
			},
			openUrl(){
				uni.openUrl({
					url: this.videoResult.video
				})
				// uni.navigateTo({ url: 'plugin://NativePlugin/webview?url='+this.videoResult.video })
			},
			// 获取短链接
			getAllVoice(){
				let openId = uni.getStorageSync("openid")
				let param= {
					userName:openId,
					realUrl:this.videoResult.video
				}
				this.$api.getAllVoice(param,res=>{
					if (res.code == 200) {
						this.allVoice = res.data
					} else {
						this.$common.errorToShow(res.msg)
					}
				})
			},
			cleanAll() {
				this.text = "";
				this.audioUrl = "";
				this.isParse = false
			},
			getClip() {
				let that = this
				uni.getClipboardData({
					success: function(res) {
						console.log(res)
						that.text = res.data;

					}
				});
			},
			parseTxt() {
				this.audioUrl = ""
				if (this.text == "") {
					this.$common.errorToShow("请输入文本")
					return
				}
				let openId = uni.getStorageSync("openid")
				let data= {
					username:openId,
					text:this.text,
					rate:this.rate,
					modeType:this.selVoice
				}
							
				this.$api.ttsAudioSync(data, (res) => {
					console.log(res)
					if (res.code == 200) {
						this.isParse = true
						this.downloadShow=true
						// 异步开始获取结果
						this.getResult()
					} else {
						this.$common.errorToShow(res.msg)
						this.isParse = false
					}
				})
			},
			getResult(){
				let that = this
				let openId = uni.getStorageSync("openid")
				let data= {
					username:openId,
				}
				this.$api.getSyncResult(data,res=>{
					if (res.code == 200){
						if (res.msg == "load" ){
							setTimeout(()=>{
								that.getResult()
							},500)
							return 
						}
						this.audioUrl = res.msg
						this.downloadShow=false
						this.playAudio()
						uni.showToast({title:"合成成功！",duration: 2000})
					}else{
						this.downloadShow=false
						uni.showToast({
							title: err.errMsg,
							duration: 2000
						})
					}
				})
			},
			playAudio(){
				this.audioList=[];
				this.audioList.push({
					title:"",
					fileUrl:this.audioUrl,
				})
				
			},
		}
	}
</script>

<style lang="scss" scoped>
	.basic-dy-parse {
		background-color: $tn-bg-gray-color;
		height: 100vh;
		position: relative;
	}

	.video-form {
		box-sizing: border-box;
		padding: 10rpx;

		.video-input {
			display: flex;
			padding: 10rpx 20rpx;
			background-color: #fff;
			border-radius: 8rpx;
			margin-bottom: 10rpx;

			.video-input-text {
				flex: 1;
			}
		}

		.video-submit {
			.button-group {
				margin-bottom: 10rpx;
			}
			.btn {
				margin: 5px 2px;
			}
		}

		.audio-warper{
			position: absolute;
			bottom: 0;
			box-sizing: border-box;
			padding: 20rpx 0upx 10upx 0 ;
			border-radius: 10upx 10upx 0 0;
			background: #000;
		}
		.bottom-text {
			text-align: center;
			font-size: 28rpx;
		}
	}
</style>