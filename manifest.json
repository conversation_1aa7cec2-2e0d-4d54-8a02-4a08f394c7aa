{
    "name" : "cyg-tool",
    "appid" : "__UNI__90E8EFE",
    "description" : "翠语阁工具箱",
    "versionName" : "1.0",
    "versionCode" : 15,
    "transformPx" : false,
    "uniStatistics" : {
        "enable" : false //全局关闭统计 
    },
    "app-plus" : {
        /* 5+App特有相关 */
        "modules" : {
            "Payment" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "appid" : "wxf62e2f29f15741af"
                    },
                    "alipay" : {
                        "scheme" : ""
                    }
                }
            }
        },
        "splashscreen" : {
            "waiting" : true
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        // "appid" : "wx687afcec8664cd74",
        "appid" : "wx4733190ba17c3946",
        "setting" : {
            "urlCheck" : true,
            "postcss" : true,
            "minified" : true,
            "es6" : true
        },
        // "plugins": {
        //        "live-player-plugin": {
        //            "version": "1.0.7",
        //            "provider": "wx2b03c6e691cd7370"
        //        }
        //    },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "用于获取当前位置的天气信息"
            }
        },
        "usingComponents" : false,
		"libVersion":"latest"
    },
    "h5" : {
        "title" : "东园百货",
        "domain" : "",
        "router" : {
            "base" : "/wap/",
            "mode" : "history"
        },
        "template" : "index.html",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "AEIBZ-H5TRI-A6VGA-5KRNA-QKKK6-JGB33"
                }
            }
        }
    },
    "mp-toutiao" : {
        "setting" : {
            "es6" : true,
            "postcss" : true,
            "minified" : true
        },
        "appid" : "ttfb828d3f64a89680"
    },
    "mp-qq" : {
        "setting" : {
            "es6" : true,
            "postcss" : true,
            "minified" : true
        }
    }
}
