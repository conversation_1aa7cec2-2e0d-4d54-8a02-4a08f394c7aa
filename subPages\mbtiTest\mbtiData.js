// MBTI 16种性格类型详细数据配置
export const mbtiTypeData = {
	'ENFP': {
		type: 'ENFP',
		typeName: '竞选者',
		description: '竞选者人格类型的人是真正的自由精神。他们通常是聚会的焦点，但与探索者角色组不同，他们不太关心当下的兴奋或快感，而是享受与他人的社交和情感联系。充满热情、有创造力、社交能力强、自由奔放，总是能够找到笑的理由。',
		tendencies: [
			{ label: '外向 (E)', percentage: 75, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 68, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 82, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 71, color: '#96CEB4' }
		],
		leadershipMode: '激励型领导者，善于鼓舞团队士气，通过个人魅力和热情来影响他人，注重团队成员的个人发展和创造力发挥。',
		learningMode: '体验式学习者，喜欢通过实践和互动来学习，偏好小组讨论、案例分析和创意项目，需要多样化的学习环境来保持兴趣。',
		problemSolvingMode: '创新思维解决问题，善于从多个角度思考问题，喜欢头脑风暴和团队协作，能够快速产生新颖的解决方案。',
		workMode: '灵活自主的工作方式，需要自由度和创造空间，适合项目制工作，喜欢与人合作但也需要独立思考的时间。',
		weaknesses: [
			'容易分心，难以专注于单一任务',
			'可能过于理想化，忽视现实限制',
			'情绪波动较大，容易受他人影响',
			'有时缺乏耐心处理细节工作'
		],
		development: '建议培养更好的时间管理能力，学会设定优先级和专注于重要任务。同时，可以通过冥想或其他方式来提高情绪稳定性，并学会在理想与现实之间找到平衡。',
		suitableFields: ['创意产业', '教育培训', '市场营销', '人力资源', '咨询服务', '媒体传播'],
		suitableCareers: ['创意总监', '培训师', '市场营销专员', '人力资源专家', '心理咨询师', '记者/编辑', '活动策划师', '产品经理']
	},
	
	'INFP': {
		type: 'INFP',
		typeName: '调停者',
		description: '调停者人格类型的人是真正的理想主义者，即使在最黑暗的时刻，他们也会寻找希望的迹象。他们可能看起来安静内向，但他们内心充满激情和创造力。',
		tendencies: [
			{ label: '内向 (I)', percentage: 78, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 72, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 85, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 69, color: '#96CEB4' }
		],
		leadershipMode: '价值观导向的领导者，通过个人价值观和理念来影响他人，善于激发团队成员的内在动机。',
		learningMode: '反思型学习者，喜欢独立思考和深度学习，偏好个人化的学习方式和自主探索。',
		problemSolvingMode: '直觉式解决问题，善于从情感和价值观角度思考问题，注重解决方案的人文关怀。',
		workMode: '独立自主的工作方式，需要安静的环境和充分的思考时间，适合创意性和有意义的工作。',
		weaknesses: [
			'过于理想化，可能忽视现实约束',
			'对批评过于敏感',
			'难以处理冲突和对抗',
			'有时过于完美主义'
		],
		development: '学会接受现实的限制，提高抗压能力和处理冲突的技巧，同时保持理想主义的初心。',
		suitableFields: ['艺术创作', '心理咨询', '社会工作', '教育', '非营利组织', '文学创作'],
		suitableCareers: ['作家', '心理咨询师', '社会工作者', '艺术家', '教师', '非营利组织工作者', '翻译', '编辑']
	},
	
	'ENFJ': {
		type: 'ENFJ',
		typeName: '主人公',
		description: '主人公人格类型的人是天生的领导者，充满激情和魅力，能够鼓舞听众。他们为帮助他人实现潜力和做正确的事而感到自豪。',
		tendencies: [
			{ label: '外向 (E)', percentage: 80, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 75, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 88, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 73, color: '#96CEB4' }
		],
		leadershipMode: '魅力型领导者，善于激励和指导他人，能够建立强有力的团队凝聚力和共同愿景。',
		learningMode: '互动式学习者，喜欢通过讨论和合作来学习，善于从他人的经验中获得启发。',
		problemSolvingMode: '协作式解决问题，善于整合不同观点，注重解决方案对人的影响。',
		workMode: '团队导向的工作方式，喜欢与人合作，善于协调和组织，适合领导和管理角色。',
		weaknesses: [
			'可能过于关注他人需求而忽视自己',
			'对批评过于敏感',
			'有时过于理想化',
			'难以做出艰难的决定'
		],
		development: '学会平衡自己和他人的需求，提高客观分析能力，培养做艰难决定的勇气。',
		suitableFields: ['教育', '人力资源', '咨询', '非营利组织', '政治', '宗教'],
		suitableCareers: ['教师', '培训师', '人力资源经理', '咨询师', '政治家', '社会活动家', '牧师', '心理学家']
	},
	
	'INFJ': {
		type: 'INFJ',
		typeName: '提倡者',
		description: '提倡者人格类型的人很少见，但他们确实对世界产生了影响。他们是理想主义者和完美主义者，但这并不是不切实际的，因为他们能够采取具体步骤来实现目标并产生持久的积极影响。',
		tendencies: [
			{ label: '内向 (I)', percentage: 82, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 79, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 76, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 81, color: '#96CEB4' }
		],
		leadershipMode: '愿景型领导者，通过深刻的洞察力和长远的愿景来引导他人，注重价值观的传递。',
		learningMode: '深度学习者，喜欢独立思考和系统性学习，善于从理论中提取实践指导。',
		problemSolvingMode: '洞察式解决问题，善于看到问题的本质和长远影响，注重系统性的解决方案。',
		workMode: '独立专注的工作方式，需要安静的环境和深度思考的时间，适合有意义和有影响力的工作。',
		weaknesses: [
			'过于完美主义',
			'容易感到疲惫和压力',
			'有时过于敏感',
			'难以处理冲突'
		],
		development: '学会接受不完美，提高压力管理能力，培养处理冲突的技巧。',
		suitableFields: ['心理学', '教育', '艺术', '写作', '咨询', '非营利组织'],
		suitableCareers: ['心理学家', '咨询师', '作家', '艺术家', '教师', '社会工作者', '研究员', '策划师']
	},
	
	'ENTP': {
		type: 'ENTP',
		typeName: '辩论家',
		description: '辩论家人格类型的人是终极的魔鬼代言人，在任何话题或想法上都能流利地争论。他们喜欢知识的挑战，无论是为了自己还是为了发展他人的理解。',
		tendencies: [
			{ label: '外向 (E)', percentage: 77, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 84, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 71, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 79, color: '#96CEB4' }
		],
		leadershipMode: '创新型领导者，善于挑战现状和激发创新思维，通过智慧和辩论能力来影响他人。',
		learningMode: '探索式学习者，喜欢通过辩论和讨论来学习，善于从不同角度理解问题。',
		problemSolvingMode: '创新思维解决问题，善于跳出传统框架，能够快速产生多种解决方案。',
		workMode: '灵活多变的工作方式，喜欢挑战和变化，适合需要创新和策略思考的工作。',
		weaknesses: [
			'容易厌倦重复性工作',
			'有时过于争论',
			'可能忽视细节',
			'难以坚持长期项目'
		],
		development: '学会专注和坚持，提高执行力，培养对细节的关注。',
		suitableFields: ['创业', '咨询', '法律', '政治', '媒体', '科技'],
		suitableCareers: ['企业家', '律师', '顾问', '政治家', '记者', '产品经理', '投资分析师', '创新专家']
	},
	
	'INTP': {
		type: 'INTP',
		typeName: '逻辑学家',
		description: '逻辑学家人格类型的人为他们独特的观点和旺盛的智力而感到自豪。他们无法忍受无聊，虽然他们很少表现出来，但他们的思想过程从未停止。',
		tendencies: [
			{ label: '内向 (I)', percentage: 81, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 86, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 83, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 74, color: '#96CEB4' }
		],
		leadershipMode: '专家型领导者，通过专业知识和逻辑分析来影响他人，注重理性和客观性。',
		learningMode: '理论型学习者，喜欢独立研究和深度思考，善于从抽象概念中理解本质。',
		problemSolvingMode: '逻辑分析解决问题，善于系统性思考和理论建构，注重解决方案的逻辑性。',
		workMode: '独立自主的工作方式，需要充分的思考时间和自由度，适合研究和分析性工作。',
		weaknesses: [
			'可能过于理论化',
			'有时忽视情感因素',
			'难以处理人际关系',
			'可能拖延执行'
		],
		development: '学会关注实际应用，提高人际交往能力，培养执行力。',
		suitableFields: ['科学研究', '技术', '学术', '工程', '数学', '哲学'],
		suitableCareers: ['科学家', '研究员', '工程师', '程序员', '数学家', '哲学家', '分析师', '建筑师']
	},

	'ENTJ': {
		type: 'ENTJ',
		typeName: '指挥官',
		description: '指挥官人格类型的人是天生的领导者。拥有这种人格类型的人体现了领导力的天赋，以魅力和自信来团结人们为共同目标而努力。',
		tendencies: [
			{ label: '外向 (E)', percentage: 85, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 78, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 89, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 87, color: '#96CEB4' }
		],
		leadershipMode: '战略型领导者，善于制定长远规划和目标，通过强有力的执行力来推动团队前进。',
		learningMode: '目标导向学习者，喜欢高效的学习方式，善于从实践中总结经验和规律。',
		problemSolvingMode: '系统性解决问题，善于分析复杂情况，能够制定全面的解决策略。',
		workMode: '高效执行的工作方式，善于组织和管理，适合领导和决策角色。',
		weaknesses: [
			'可能过于强势和控制欲强',
			'有时忽视他人感受',
			'对效率要求过高',
			'可能缺乏耐心'
		],
		development: '学会倾听他人意见，提高情商和同理心，培养耐心和包容性。',
		suitableFields: ['管理', '商业', '法律', '政治', '军事', '金融'],
		suitableCareers: ['CEO', '总经理', '律师', '政治家', '军官', '投资银行家', '管理顾问', '企业家']
	},

	'INTJ': {
		type: 'INTJ',
		typeName: '建筑师',
		description: '建筑师人格类型的人富有想象力和战略性思维，对一切都有计划。他们是完美主义者，喜欢独立工作，对自己和他人都有很高的标准。',
		tendencies: [
			{ label: '内向 (I)', percentage: 84, color: '#FF6B6B' },
			{ label: '直觉 (N)', percentage: 88, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 81, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 86, color: '#96CEB4' }
		],
		leadershipMode: '愿景型领导者，通过长远的战略规划和深刻的洞察力来引导团队。',
		learningMode: '自主学习者，喜欢深度研究和系统性学习，善于构建知识体系。',
		problemSolvingMode: '战略性解决问题，善于从全局角度分析问题，制定长期解决方案。',
		workMode: '独立专注的工作方式，需要安静的环境和充分的思考时间。',
		weaknesses: [
			'可能过于完美主义',
			'有时显得冷漠和距离感',
			'对他人期望过高',
			'可能忽视情感因素'
		],
		development: '学会与他人协作，提高沟通技巧，培养对他人的理解和包容。',
		suitableFields: ['科学', '技术', '工程', '研究', '战略规划', '学术'],
		suitableCareers: ['科学家', '工程师', '研究员', '战略规划师', '系统分析师', '建筑师', '投资分析师', '教授']
	},

	'ESTJ': {
		type: 'ESTJ',
		typeName: '总经理',
		description: '总经理人格类型的人是优秀的管理者，在管理事务或人员方面无与伦比。他们善于将人们聚集在一起完成任务。',
		tendencies: [
			{ label: '外向 (E)', percentage: 82, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 79, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 85, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 88, color: '#96CEB4' }
		],
		leadershipMode: '执行型领导者，善于组织和管理，通过明确的规则和流程来推动工作。',
		learningMode: '实践型学习者，喜欢通过实际操作来学习，重视经验和传统方法。',
		problemSolvingMode: '系统化解决问题，善于运用既有的方法和流程来解决问题。',
		workMode: '有序高效的工作方式，善于制定计划和执行，适合管理和运营角色。',
		weaknesses: [
			'可能过于固执和保守',
			'有时忽视创新和变化',
			'对他人要求严格',
			'可能缺乏灵活性'
		],
		development: '学会接受变化和创新，提高灵活性，培养对不同观点的开放态度。',
		suitableFields: ['管理', '行政', '金融', '法律', '军事', '制造业'],
		suitableCareers: ['总经理', '行政主管', '财务经理', '法官', '军官', '项目经理', '运营经理', '银行经理']
	},

	'ISTJ': {
		type: 'ISTJ',
		typeName: '物流师',
		description: '物流师人格类型的人是可靠的实用主义者，他们有事实和可靠性的倾向。他们喜欢秩序和组织，在工作和家庭中都是如此。',
		tendencies: [
			{ label: '内向 (I)', percentage: 79, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 82, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 76, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 85, color: '#96CEB4' }
		],
		leadershipMode: '稳健型领导者，通过可靠性和一致性来建立信任，善于维护组织秩序和传统。',
		learningMode: '循序渐进学习者，喜欢结构化的学习方式，重视实践经验和具体案例。',
		problemSolvingMode: '系统化解决问题，善于运用既有经验和标准流程来解决问题。',
		workMode: '有条不紊的工作方式，注重细节和准确性，适合需要精确性和可靠性的工作。',
		weaknesses: [
			'可能过于保守和抗拒变化',
			'有时缺乏创新思维',
			'对新想法接受较慢',
			'可能过于注重细节而忽视大局'
		],
		development: '学会接受变化和新想法，培养创新思维，提高适应性和灵活性。',
		suitableFields: ['会计', '审计', '法律', '医疗', '工程', '行政管理'],
		suitableCareers: ['会计师', '审计师', '律师', '医生', '工程师', '行政人员', '银行职员', '质量控制专员']
	},

	'ISFJ': {
		type: 'ISFJ',
		typeName: '守护者',
		description: '守护者人格类型的人是真正的利他主义者，以善良和温暖的心对待他人，总是愿意为重要的人和事业做出贡献。',
		tendencies: [
			{ label: '内向 (I)', percentage: 77, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 74, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 83, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 80, color: '#96CEB4' }
		],
		leadershipMode: '服务型领导者，通过关怀和支持来影响他人，注重团队成员的福祉和发展。',
		learningMode: '协作式学习者，喜欢在支持性环境中学习，重视实际应用和他人经验。',
		problemSolvingMode: '关怀式解决问题，善于考虑解决方案对人的影响，注重和谐与合作。',
		workMode: '细致周到的工作方式，注重服务他人和维护和谐，适合支持性和服务性工作。',
		weaknesses: [
			'可能过于自我牺牲',
			'难以拒绝他人请求',
			'对批评过于敏感',
			'有时缺乏自信'
		],
		development: '学会设定边界，提高自信心，培养表达自己需求的能力。',
		suitableFields: ['教育', '医疗', '社会工作', '人力资源', '客户服务', '非营利组织'],
		suitableCareers: ['教师', '护士', '社会工作者', '人力资源专员', '客服代表', '图书管理员', '心理咨询师', '行政助理']
	},

	'ESFJ': {
		type: 'ESFJ',
		typeName: '执政官',
		description: '执政官人格类型的人非常关心他人的感受，他们是社交能力很强的人，在帮助他人方面表现出色，总是渴望做正确的事。',
		tendencies: [
			{ label: '外向 (E)', percentage: 81, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 76, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 86, color: '#45B7D1' },
			{ label: '判断 (J)', percentage: 78, color: '#96CEB4' }
		],
		leadershipMode: '协调型领导者，善于建立团队和谐，通过关怀和支持来激励团队成员。',
		learningMode: '互动式学习者，喜欢在团队环境中学习，重视实践应用和他人反馈。',
		problemSolvingMode: '协作式解决问题，善于整合不同意见，注重解决方案的可行性和人文关怀。',
		workMode: '团队协作的工作方式，善于沟通协调，适合需要人际交往和服务他人的工作。',
		weaknesses: [
			'可能过于在意他人看法',
			'难以处理冲突',
			'有时缺乏创新思维',
			'可能忽视自己的需求'
		],
		development: '学会独立思考，提高处理冲突的能力，培养创新意识。',
		suitableFields: ['教育', '医疗', '销售', '公关', '活动策划', '客户服务'],
		suitableCareers: ['教师', '护士', '销售代表', '公关专员', '活动策划师', '客户经理', '社区工作者', '接待员']
	},

	'ESTP': {
		type: 'ESTP',
		typeName: '企业家',
		description: '企业家人格类型的人总是对周围环境产生影响。在聚会中发现他们的最好方法就是找到人群中心那个有着传染性笑声的人。他们喜欢成为关注的焦点。',
		tendencies: [
			{ label: '外向 (E)', percentage: 84, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 81, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 73, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 79, color: '#96CEB4' }
		],
		leadershipMode: '行动型领导者，善于在压力下做决定，通过实际行动和个人魅力来影响他人。',
		learningMode: '实践型学习者，喜欢通过动手操作来学习，重视即时反馈和实际应用。',
		problemSolvingMode: '快速反应解决问题，善于在紧急情况下做出决策，注重实用性和效率。',
		workMode: '灵活机动的工作方式，喜欢变化和挑战，适合需要快速反应和实际操作的工作。',
		weaknesses: [
			'可能缺乏长远规划',
			'有时过于冲动',
			'难以专注于理论学习',
			'可能忽视他人感受'
		],
		development: '学会制定长期计划，提高耐心和专注力，培养对他人感受的敏感度。',
		suitableFields: ['销售', '体育', '娱乐', '紧急服务', '创业', '旅游'],
		suitableCareers: ['销售经理', '运动员', '演员', '急救人员', '企业家', '导游', '警察', '消防员']
	},

	'ISTP': {
		type: 'ISTP',
		typeName: '鉴赏家',
		description: '鉴赏家人格类型的人喜欢用双手探索和检查周围的世界，以冷静的理性主义和旺盛的好奇心来看待周围的事物。',
		tendencies: [
			{ label: '内向 (I)', percentage: 76, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 83, color: '#4ECDC4' },
			{ label: '思维 (T)', percentage: 78, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 74, color: '#96CEB4' }
		],
		leadershipMode: '技术型领导者，通过专业技能和实际能力来获得尊重，善于解决技术难题。',
		learningMode: '动手实践学习者，喜欢通过实际操作来理解原理，重视技能的掌握。',
		problemSolvingMode: '技术性解决问题，善于分析机械和系统问题，注重实用性和效率。',
		workMode: '独立操作的工作方式，喜欢有形的工作成果，适合技术性和操作性工作。',
		weaknesses: [
			'可能缺乏长期承诺',
			'有时显得冷漠',
			'难以表达情感',
			'可能避免复杂的人际关系'
		],
		development: '学会表达情感，提高人际交往能力，培养长期规划意识。',
		suitableFields: ['工程', '技术', '制造', '维修', '建筑', '农业'],
		suitableCareers: ['机械工程师', '技术员', '飞行员', '外科医生', '建筑师', '程序员', '维修工', '农民']
	},

	'ESFP': {
		type: 'ESFP',
		typeName: '娱乐家',
		description: '娱乐家人格类型的人是真正的艺人，他们热情、友好，喜欢成为关注的焦点。这些人在鼓励他人时是最快乐的。',
		tendencies: [
			{ label: '外向 (E)', percentage: 83, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 77, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 81, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 76, color: '#96CEB4' }
		],
		leadershipMode: '鼓舞型领导者，通过热情和积极态度来激励团队，善于营造轻松愉快的工作氛围。',
		learningMode: '体验式学习者，喜欢在轻松愉快的环境中学习，重视互动和实际体验。',
		problemSolvingMode: '创意性解决问题，善于从人的角度思考问题，注重解决方案的人性化。',
		workMode: '活跃互动的工作方式，喜欢与人合作，适合需要创意和人际交往的工作。',
		weaknesses: [
			'可能缺乏长期规划',
			'有时过于感性',
			'难以处理批评',
			'可能避免冲突'
		],
		development: '学会制定计划，提高理性分析能力，培养处理冲突的技巧。',
		suitableFields: ['娱乐', '艺术', '教育', '销售', '旅游', '时尚'],
		suitableCareers: ['演员', '艺术家', '教师', '销售代表', '导游', '时尚设计师', '摄影师', '活动策划师']
	},

	'ISFP': {
		type: 'ISFP',
		typeName: '探险家',
		description: '探险家人格类型的人是真正的艺术家，但不一定是典型意义上的艺术家。对这些人来说，生活就是一张等待绘制的画布。',
		tendencies: [
			{ label: '内向 (I)', percentage: 78, color: '#FF6B6B' },
			{ label: '感觉 (S)', percentage: 72, color: '#4ECDC4' },
			{ label: '情感 (F)', percentage: 84, color: '#45B7D1' },
			{ label: '感知 (P)', percentage: 75, color: '#96CEB4' }
		],
		leadershipMode: '价值观导向领导者，通过个人价值观和真诚来影响他人，善于激发他人的创造力。',
		learningMode: '个性化学习者，喜欢按照自己的节奏学习，重视个人兴趣和价值观的契合。',
		problemSolvingMode: '直觉式解决问题，善于从情感和美学角度思考问题，注重解决方案的和谐性。',
		workMode: '自由创作的工作方式，需要灵活性和创造空间，适合艺术性和个性化的工作。',
		weaknesses: [
			'可能过于敏感',
			'难以处理批评',
			'有时缺乏自信',
			'可能避免竞争'
		],
		development: '学会接受建设性批评，提高自信心，培养竞争意识和抗压能力。',
		suitableFields: ['艺术', '设计', '音乐', '写作', '心理咨询', '环保'],
		suitableCareers: ['艺术家', '设计师', '音乐家', '作家', '心理咨询师', '环保工作者', '摄影师', '治疗师']
	}
};

// 获取MBTI类型数据的函数
export function getMbtiData(type) {
	return mbtiTypeData[type] || mbtiTypeData['ENFP']; // 默认返回ENFP
}

// 计算倾向性百分比的函数
export function calculateTendencies(answers) {
	const tendencies = [];
	
	// E vs I
	const ePercentage = Math.round((answers.E / (answers.E + answers.I)) * 100);
	tendencies.push({
		label: ePercentage > 50 ? '外向 (E)' : '内向 (I)',
		percentage: Math.max(ePercentage, 100 - ePercentage),
		color: '#FF6B6B'
	});
	
	// N vs S
	const nPercentage = Math.round((answers.N / (answers.N + answers.S)) * 100);
	tendencies.push({
		label: nPercentage > 50 ? '直觉 (N)' : '感觉 (S)',
		percentage: Math.max(nPercentage, 100 - nPercentage),
		color: '#4ECDC4'
	});
	
	// F vs T
	const fPercentage = Math.round((answers.F / (answers.F + answers.T)) * 100);
	tendencies.push({
		label: fPercentage > 50 ? '情感 (F)' : '思维 (T)',
		percentage: Math.max(fPercentage, 100 - fPercentage),
		color: '#45B7D1'
	});
	
	// J vs P
	const jPercentage = Math.round((answers.J / (answers.J + answers.P)) * 100);
	tendencies.push({
		label: jPercentage > 50 ? '判断 (J)' : '感知 (P)',
		percentage: Math.max(jPercentage, 100 - jPercentage),
		color: '#96CEB4'
	});
	
	return tendencies;
}
