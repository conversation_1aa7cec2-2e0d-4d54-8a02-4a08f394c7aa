<template>
  <view class="basic-invoice-parse tn-safe-area-inset-bottom">
    <!-- 页面内容 -->
    <view class="video-form">
      <view class="video-input">
        <view class="upimg">
          <tn-image-upload
            ref="imageUpload"
            :autoUpload="false"
            :action="action"
            maxCount="1"
            :fileList="fileList"
            :formData="uploadData"
            :customBtn="true"
            width="400"
            height="400"
            @on-success="onUploadSucc"
            @on-list-change="listChange"
          >
            <view
              slot="addBtn"
              class="tn-image-upload__custom-btn"
              hover-class="tn-hover-class"
              hover-stay-time="150"
            >
              <view>选择图片</view>
            </view>
          </tn-image-upload>
        </view>
        <view class="button-group">
          <tn-button
            :shadow="true"
            v-if="!isParse && !showLoading"
            class="btn"
            height="100rpx"
            backgroundColor="#3D7EFF"
            fontColor="#FFFFFF"
            @click="upload"
            >识别</tn-button
          >
        </view>
      </view>

      <view class="video-result">
        <view class="video-info" v-if="isParse">
          <view class="video-info-item">
            <view class="item-text-title">
              <view class="item-text-label">发票号</view>
              <view>{{ checkResult.invoiceCode }}</view>
            </view>
            <tn-button shape="icon" @click="copyText(checkResult.invoiceCode)">
              <text class="tn-icon-copy"></text>
            </tn-button>
          </view>
          <view class="video-info-item">
            <view class="item-text-title">
              <view class="item-text-label">开票日期</view>
              <view>{{ checkResult.invoiceDate }}</view>
            </view>
          </view>
          <view class="video-info-item">
            <view class="item-text">
              <view class="item-text-label">购方名称</view>
              <view>{{ checkResult.buyerName }}</view>
            </view>
          </view>
          <view class="video-info-item">
            <view class="item-text">
              <view class="item-text-label">发票金额</view>
              <view>{{ checkResult.buyMoney }}</view>
            </view>
          </view>
          <view class="video-info-item">
            <view class="item-text">
              <view class="item-text-label">是否已核验</view>
              <view>{{
                checkResult.isChecked == 1 ? "已核验" : "未核验"
              }}</view>
            </view>
          </view>
          <view v-if="checkResult.isChecked == 1" class="video-info-item">
            <view class="item-text">
              <view class="item-text-label">核验日期：</view>
              <view>{{ checkResult.checkTime }}</view>
            </view>
          </view>
        </view>
        <tn-loading mode="circle" :show="showLoading">识别中...</tn-loading>
        <tn-empty mode="data" v-if="!isParse && !showLoading"></tn-empty>
      </view>
      <view class="button-group" v-if="isParse">
        <tn-button :shadow="true" class="btn" height="100rpx" @click="cleanAll"
          >清空</tn-button
        >
        <tn-button
          :shadow="true"
          class="btn"
          height="100rpx"
          backgroundColor="#3D7EFF"
          fontColor="#FFFFFF"
          @click="invoiceSave"
          v-if="checkResult.isChecked == 0"
          >提交</tn-button
        >
      </view>
    </view>
    <view class="bottom-text"> 识别完毕，点击提交才会标记重复 </view>
    <tn-modal
      v-model="showAdDlg"
      :content="adMsg"
      :button="button"
      :maskCloseable="false"
      @click="onClickBtn"
    ></tn-modal>
  </view>
</template>

<script>
export default {
  name: "InvoiceCheck",
  components: {},
  data() {
    return {
      action: "https://up-z0.qiniup.com",
      // action: "http://upload.qiniup.com/",
      checkResult: {
        id: 0,
        invoiceCode: "",
        invoiceDate: "2025年05月08日",
        buyerName: "",
        buyerCode: "",
        buyMoney: "525.00",
        totalToken: 1023,
        checkTime: "2025-06-20 22:52:21",
        checkUser: "12345",
        isChecked: 0,
      },
      uploadData: {},
      isParse: false,
      downloadShow: false,
      progress: 0,
      shortUrl: "",
      rewardedVideoAd: null,
      isSendGift: false,
      showAdDlg: false,
      adMsg: "您将观看一段广告，来获取资源权限",
      button: [
        {
          text: "取消",
          backgroundColor: "#E6E6E6",
          plain: true,
        },
        {
          text: "播放",
          backgroundColor: "tn-bg-indigo",
          fontColor: "#FFFFFF",
        },
      ],
      fileList: [],
      showLoading: false,
    };
  },
  onLoad() {
    this.isSendGift = false;
    this.rewardedVideoAd = uni.createRewardedVideoAd({
      adUnitId: "adunit-c849be5752274263",
    });
    this.rewardedVideoAd.onLoad(() => {
      console.log("激励视频 广告加载成功");
    });
    this.rewardedVideoAd.onError((err) => {
      uni.showToast({
        title: "视频加载失败，请重新获取",
        duration: 2000,
      });
    });
    this.rewardedVideoAd.onClose((res) => {
      // 用户点击了【关闭广告】按钮
      if (res && res.isEnded) {
        this.isSendGift = true;
        uni.showToast({
          title: "已经获取奖励",
          duration: 2000,
        });
      } else {
        uni.showToast({
          title: "请完整观看视频，获取奖励",
          duration: 2000,
        });
        // 播放中途退出，不下发游戏奖励
      }
    });
  },
  methods: {
    onClickBtn(opt) {
      if (opt.index == 1) {
        this.showAd();
      }
      this.showAdDlg = false;
    },
    openAdDlg() {
      this.showAdDlg = true;
    },
    copyText(text) {
      // if (!this.isSendGift){
      // 	this.openAdDlg()
      // 	return
      // }
      if (text == "") {
        return;
      }
      uni.setClipboardData({
        data: text,
        success: function () {
          uni.showToast({
            title: "已复制到剪切板",
            duration: 2000,
          });
        },
      });
    },
    showAd() {
      let that = this;
      this.rewardedVideoAd.show().then(() => {
        console.log("激励视频 广告显示");
      });
    },

    cleanAll() {
      this.videoResult = {};
      this.fileList = [];
      this.$refs.imageUpload.clear();
      this.isParse = false;
    },
    onUploadSucc(data) {
      let openId = uni.getStorageSync("openid");
      console.log(data);
      let param = {
        imgUrl: `http://res.bogerj.cn/${data.key}`,
        username: openId,
      };
      this.showLoading = true;
      this.$api.invoiceCheck(param, (res) => {
        this.isParse = true;
        if (res.code == 200) {
          this.checkResult = res.data;
        }
        this.showLoading = false;
      });
    },
    listChange(lists, index) {
      console.log("上传文件列表发生改变", lists, index);
      this.fileList = lists;
    },
    upload() {
      console.log(this.fileList);
      if (!this.fileList || this.fileList.length == 0) {
        uni.showToast({
          title: "请选择发票图片",
          icon: "none",
          duration: 1000,
        });
        return;
      }
      this.$api.getUploadToken({}, (res) => {
        if (res.code == 200) {
          let fs = new Date().getTime();
          this.uploadData = {
            token: res.data,
            key: `invoice/${fs}.jpg`,
          };
          this.$nextTick(() => {
            this.$refs.imageUpload.upload();
          });
        }
      });
    },
    invoiceSave() {
      this.$api.invoiceSave(this.checkResult, (res) => {
        if (res.code == 200) {
          uni.showToast({
            title: "提交成功",
            icon: "none",
            duration: 1000,
          });
          this.cleanAll();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.basic-invoice-parse {
  background-color: $tn-bg-gray-color;
  height: 100vh;
  .video-form {
    box-sizing: border-box;

    padding: 10rpx;

    .video-input {
      width: 100%;
      text-align: center;
      padding: 10rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      margin-bottom: 10rpx;
    }
    .upimg {
      margin-bottom: 10rpx;
    }
    .button-group {
      text-align: center;
      margin-bottom: 10rpx;
    }
    .btn {
      margin: 5px 2px;
    }
    .video-result {
      box-sizing: border-box;
      padding: 20rpx;
      height: 380rpx;
      background-color: #fff;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      .video-img-div {
        width: 100%;
        height: 200px;
        background-color: #080808;
        text-align: center;
      }

      .video-info {
        padding-top: 20px;

        .video-info-item {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 28rpx;

          .item-text,
          .item-text-title {
            flex: 1;
            display: flex;
            line-height: 50rpx;
            height: 50rpx;
            .item-text-label {
              width: 160rpx;
              padding-right: 20rpx;
              text-align: right;
            }
          }
        }
      }
    }
  }
  .bottom-text {
    text-align: center;
    font-size: 28rpx;
  }
  .tn-image-upload__custom-btn {
    background-color: #e6e6e6;
    width: 400rpx;
    height: 400rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rpx;
    margin: 0 auto;
  }
  .tn-image-upload__item {
    width: 100%;
    height: 180rpx;
    border-radius: 30rpx;
    margin-bottom: 20rpx;

    &__image {
      width: 100%;
      height: 180rpx;
      border-radius: 30rpx;
    }
  }
}
</style>
<style>
.tn-image-upload__item-preview {
  margin: 0 auto !important;
}
</style>