/**
 * 页面展示列表数据
 */
export default {
  data: [{
      title: '圈子博客',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'order',
          title: '操作指引',
          author: '图鸟科技',
          url: '/vipPage/life/guide/guide'
        },{
          icon: 'order',
          title: '首次指引',
          author: '图鸟科技',
          url: '/vipPage/life/start/start'
        },
        {
          icon: 'order',
          title: '圈子首页',
          author: '图鸟科技',
          url: '/vipPage/blog/blog/blog'
        },
        {
          icon: 'order',
          title: '社交圈子',
          author: '图鸟科技',
          url: '/vipPage/blog/socialize/socialize'
        },
        {
          icon: 'order',
          title: '简约圈子(旧)',
          author: '图鸟科技',
          url: '/vipPage/blog/circle/circle'
        },
        {
          icon: 'order',
          title: '圈子个人',
          author: '图鸟科技',
          url: '/vipPage/blog/myblog/myblog'
        },
        {
          icon: 'order',
          title: '消息通知',
          author: '图鸟科技',
          url: '/vipPage/blog/message/message'
        },
        {
          icon: 'order',
          title: '商品优选',
          author: '图鸟科技',
          url: '/vipPage/blog/prefer/prefer'
        },
        {
          icon: 'order',
          title: '优选详情',
          author: '图鸟科技',
          url: '/vipPage/blog/product/product'
        },
        {
          icon: 'order',
          title: '博客博主',
          author: '图鸟科技',
          url: '/vipPage/blog/blogger/blogger'
        },
        {
          icon: 'order',
          title: '酷炫功能',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/cool'
        },
        {
          icon: 'order',
          title: '友情链接',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/link'
        },
        {
          icon: 'order',
          title: '祝福页面',
          author: '图鸟科技',
          url: '/vipPage/life/bless/bless'
        }
      ]
    },
    {
      title: '酷炫首页',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'order',
          title: '图鸟首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/tuniao'
        },
        {
          icon: 'order',
          title: '奶茶首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/tea'
        },
        {
          icon: 'order',
          title: '阅读首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/read'
        },
        {
          icon: 'order',
          title: '月亮首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/moon'
        },
        {
          icon: 'order',
          title: '计划首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/plan'
        },
        {
          icon: 'order',
          title: '新年首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/year'
        },
        {
          icon: 'order',
          title: '电影首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/movie'
        },
        {
          icon: 'order',
          title: '食物首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/food'
        },
        {
          icon: 'order',
          title: '拟态首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/mimicry'
        },
        {
          icon: 'order',
          title: '充电首页',
          author: '图鸟科技',
          url: '/vipPage/life/power/power'
        },
        {
          icon: 'order',
          title: '卡片首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/card'
        },
        {
          icon: 'order',
          title: '健康首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/health'
        },
        {
          icon: 'order',
          title: '全景首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/panoramic'
        },
        {
          icon: 'order',
          title: 'uCharts首页',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/ucharts'
        },
      ]
    },
    {
      title: '商城店铺',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'order',
          title: '店铺商品',
          author: '图鸟科技',
          url: '/vipPage/shop/store/store'
        },
        {
          icon: 'order',
          title: '商品订单',
          author: '图鸟科技',
          url: '/vipPage/shop/order/order'
        },
        {
          icon: 'order',
          title: '商品分类',
          author: '图鸟科技',
          url: '/vipPage/shop/classify/classify'
        },
        {
          icon: 'order',
          title: '积分活动',
          author: '图鸟科技',
          url: '/vipPage/shop/money/money'
        }
      ]
    },
    {
      title: '会员需求',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'order',
          title: 'Drag长按拖拽随机固定',
          author: '图鸟科技',
          url: '/vipPage/components/drag/basic-drag/basic-drag'
        },
        {
          icon: 'order',
          title: '图片上传长按拖拽',
          author: '图鸟科技',
          url: '/vipPage/components/drag/upload-image-drag/upload-image-drag'
        },
        {
          icon: 'order',
          title: 'Cropper图片裁剪',
          author: '图鸟科技',
          url: '/vipPage/components/cropper/cropper'
        },
        {
          icon: 'order',
          title: 'StackSwiper堆叠轮播',
          author: '图鸟科技',
          url: '/vipPage/components/stack-swiper/stack-swiper'
        },
        {
          icon: 'order',
          title: '重力效果',
          author: '图鸟科技',
          url: '/vipPage/home/<USER>/page1/page1'
        },
        {
          icon: 'order',
          title: '下拉刷新',
          author: '图鸟科技',
          url: '/vipPage/components/scroll-view/index'
        },
        {
          icon: 'order',
          title: '级联选择',
          author: '图鸟科技',
          url: '/vipPage/components/cascade-selection/cascade-selection'
        },
        {
          icon: 'order',
          title: '瀑布流',
          author: '图鸟科技',
          url: '/vipPage/components/waterfall/waterfall'
        },
        {
          icon: 'order',
          title: '树形菜单',
          author: '图鸟科技',
          url: '/vipPage/components/tree-view/tree-view'
        },
        {
          icon: 'order',
          title: '表格',
          author: '图鸟科技',
          url: '/vipPage/components/table/index'
        },
        {
          icon: 'order',
          title: '图鸟轮播(实验)',
          author: '图鸟科技',
          url: '/vipPage/components/custom-swiper/index'
        }
      ]
    },
    {
      title: '生态支持',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [{
          icon: 'order',
          title: '短视频',
          author: '图鸟 & 第三方',
          url: '/thirdPage/short-video/short-video'
        },{
          icon: 'order',
          title: '外卖模板',
          author: '图鸟 & 第三方',
          url: '/takeOutPage/walking-route/walking-route'
        },
        {
          icon: 'order',
          title: '期待你的加入',
          author: '图鸟 & 第三方',
          url: '/templatePage/life/candle/candle'
        }
      ]
    }
  ]
}
