<template>
	<view class="analyze-container">
		<!-- 页面标题 -->
		<view class="page-title">
			<text class="title-text">MBTI测试结果</text>
		</view>

		<!-- 性格类型卡片 -->
		<uni-card class="result-card" title="您的性格类型" :is-shadow="true">
			<view class="personality-type">
				<view class="type-badge">
					<text class="type-text">{{ mbtiResult.type }}</text>
				</view>
				<view class="type-name">
					<text class="name-text">{{ mbtiResult.typeName }}</text>
				</view>
			</view>
		</uni-card>

		<!-- 类型描述 -->
		<uni-card class="result-card" title="性格描述" :is-shadow="true">
			<view class="description-content">
				<text class="description-text">{{ mbtiResult.description }}</text>
			</view>
		</uni-card>

		<!-- 类型倾向性 -->
		<uni-card class="result-card" title="性格倾向" :is-shadow="true">
			<view class="tendency-list">
				<view class="tendency-item" v-for="(item, index) in mbtiResult.tendencies" :key="index">
					<view class="tendency-label">{{ item.label }}</view>
					<view class="tendency-bar">
						<view class="tendency-progress" :style="{ width: item.percentage + '%', backgroundColor: item.color }"></view>
					</view>
					<view class="tendency-value">{{ item.percentage }}%</view>
				</view>
			</view>
		</uni-card>

		<!-- 领导模式 -->
		<uni-card class="result-card" title="领导模式" :is-shadow="true">
			<view class="mode-content">
				<view class="mode-icon">👑</view>
				<text class="mode-text">{{ mbtiResult.leadershipMode }}</text>
			</view>
		</uni-card>

		<!-- 学习模式 -->
		<uni-card class="result-card" title="学习模式" :is-shadow="true">
			<view class="mode-content">
				<view class="mode-icon">📚</view>
				<text class="mode-text">{{ mbtiResult.learningMode }}</text>
			</view>
		</uni-card>

		<!-- 解决问题模式 -->
		<uni-card class="result-card" title="解决问题模式" :is-shadow="true">
			<view class="mode-content">
				<view class="mode-icon">🧩</view>
				<text class="mode-text">{{ mbtiResult.problemSolvingMode }}</text>
			</view>
		</uni-card>

		<!-- 工作模式 -->
		<uni-card class="result-card" title="工作模式" :is-shadow="true">
			<view class="mode-content">
				<view class="mode-icon">💼</view>
				<text class="mode-text">{{ mbtiResult.workMode }}</text>
			</view>
		</uni-card>

		<!-- 性格缺点 -->
		<uni-card class="result-card" title="需要注意的方面" :is-shadow="true">
			<view class="weakness-list">
				<view class="weakness-item" v-for="(weakness, index) in mbtiResult.weaknesses" :key="index">
					<view class="weakness-dot">•</view>
					<text class="weakness-text">{{ weakness }}</text>
				</view>
			</view>
		</uni-card>

		<!-- 发展建议 -->
		<uni-card class="result-card" title="发展建议" :is-shadow="true">
			<view class="development-content">
				<text class="development-text">{{ mbtiResult.development }}</text>
			</view>
		</uni-card>

		<!-- 适合领域 -->
		<uni-card class="result-card" title="适合的领域" :is-shadow="true">
			<view class="field-tags">
				<view class="field-tag" v-for="(field, index) in mbtiResult.suitableFields" :key="index">
					<text class="field-text">{{ field }}</text>
				</view>
			</view>
		</uni-card>

		<!-- 适合职业 -->
		<uni-card class="result-card" title="推荐职业" :is-shadow="true">
			<view class="career-list">
				<view class="career-item" v-for="(career, index) in mbtiResult.suitableCareers" :key="index">
					<view class="career-icon">🎯</view>
					<text class="career-text">{{ career }}</text>
				</view>
			</view>
		</uni-card>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<view class="action-btn share-btn" @click="shareResult">
				<text class="btn-text">分享结果</text>
			</view>
			<view class="action-btn retry-btn" @click="retakeTest">
				<text class="btn-text">重新测试</text>
			</view>
		</view>

		<!-- 底部间距 -->
		<view class="bottom-space"></view>
	</view>
</template>

<script>
	import { getMbtiData } from './mbtiData.js';

	export default {
		data() {
			return {
				mbtiResult: {
					type: 'ENFP',
					typeName: '竞选者',
					description: '正在加载您的测试结果...',
					tendencies: [],
					leadershipMode: '',
					learningMode: '',
					problemSolvingMode: '',
					workMode: '',
					weaknesses: [],
					development: '',
					suitableFields: [],
					suitableCareers: []
				}
			}
		},
		onLoad(options) {
			// 获取页面参数
			if (options && options.mbtiType) {
				this.getMbtiResultByType(options.mbtiType);
			}
		},
		mounted() {
			// 页面挂载完成
		},
		methods: {

			// 根据MBTI类型获取详细结果
			getMbtiResultByType(type) {
				console.log('获取MBTI类型结果:', type);

				// 从配置文件获取对应类型的详细信息
				const typeData = getMbtiData(type);
				this.mbtiResult = { ...typeData };

				// 可以在这里调用API获取更详细的个性化结果
				// this.$api.getMbtiDetailResult(type, res => {
				//     if (res.code === 200) {
				//         this.mbtiResult = res.data;
				//     }
				// });
			},

			// 分享结果
			shareResult() {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
					success: (res) => {
						switch(res.tapIndex) {
							case 0:
								this.shareToWeChat();
								break;
							case 1:
								this.shareToMoments();
								break;
							case 2:
								this.copyLink();
								break;
						}
					}
				});
			},

			// 分享到微信
			shareToWeChat() {
				uni.showToast({
					title: '分享到微信',
					icon: 'success'
				});
			},

			// 分享到朋友圈
			shareToMoments() {
				uni.showToast({
					title: '分享到朋友圈',
					icon: 'success'
				});
			},

			// 复制链接
			copyLink() {
				uni.setClipboardData({
					data: '我的MBTI测试结果：' + this.mbtiResult.type + ' - ' + this.mbtiResult.typeName,
					success: () => {
						uni.showToast({
							title: '已复制到剪贴板',
							icon: 'success'
						});
					}
				});
			},

			// 重新测试
			retakeTest() {
				uni.showModal({
					title: '确认重新测试',
					content: '重新测试将清除当前结果，是否继续？',
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/subPages/mbtiTest/index'
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
.analyze-container {
	padding: 20rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 页面标题 */
.page-title {
	text-align: center;
	padding: 30rpx 0 40rpx 0;
}

.title-text {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* 卡片样式 */
.result-card {
	margin-bottom: 30rpx;
}

/* 性格类型展示 */
.personality-type {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 0;
}

.type-badge {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.type-text {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
}

.type-name {
	text-align: center;
}

.name-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 描述内容 */
.description-content {
	padding: 20rpx 0;
}

.description-text {
	font-size: 32rpx;
	line-height: 1.6;
	color: #666;
	text-align: justify;
}

/* 倾向性列表 */
.tendency-list {
	padding: 20rpx 0;
}

.tendency-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.tendency-label {
	width: 160rpx;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.tendency-bar {
	flex: 1;
	height: 16rpx;
	background-color: #f0f0f0;
	border-radius: 8rpx;
	margin: 0 20rpx;
	overflow: hidden;
}

.tendency-progress {
	height: 100%;
	border-radius: 8rpx;
	transition: width 0.3s ease;
}

.tendency-value {
	width: 80rpx;
	text-align: right;
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

/* 模式内容 */
.mode-content {
	display: flex;
	align-items: flex-start;
	padding: 20rpx 0;
}

.mode-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
	margin-top: 10rpx;
}

.mode-text {
	flex: 1;
	font-size: 32rpx;
	line-height: 1.6;
	color: #666;
	text-align: justify;
}

/* 缺点列表 */
.weakness-list {
	padding: 20rpx 0;
}

.weakness-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.weakness-dot {
	font-size: 32rpx;
	color: #ff6b6b;
	margin-right: 15rpx;
	margin-top: 5rpx;
	font-weight: bold;
}

.weakness-text {
	flex: 1;
	font-size: 30rpx;
	line-height: 1.5;
	color: #666;
}

/* 发展建议 */
.development-content {
	padding: 20rpx 0;
}

.development-text {
	font-size: 32rpx;
	line-height: 1.6;
	color: #666;
	text-align: justify;
}

/* 领域标签 */
.field-tags {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx 0;
	gap: 20rpx;
}

.field-tag {
	padding: 15rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	margin-bottom: 15rpx;
}

.field-text {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}

/* 职业列表 */
.career-list {
	padding: 20rpx 0;
}

.career-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.career-item:last-child {
	border-bottom: none;
}

.career-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.career-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	padding: 40rpx 20rpx;
	gap: 30rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.share-btn {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.retry-btn {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.btn-text {
	font-size: 32rpx;
	color: white;
	font-weight: 600;
}

/* 底部间距 */
.bottom-space {
	height: 40rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.tendency-item {
		flex-direction: column;
		align-items: flex-start;
	}

	.tendency-label {
		width: 100%;
		margin-bottom: 15rpx;
	}

	.tendency-bar {
		width: 100%;
		margin: 0 0 10rpx 0;
	}

	.tendency-value {
		width: 100%;
		text-align: left;
	}

	.action-buttons {
		flex-direction: column;
		gap: 20rpx;
	}
}
</style>