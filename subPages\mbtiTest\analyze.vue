<template>
	<view class="analyze-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="bg-circle bg-circle-1"></view>
			<view class="bg-circle bg-circle-2"></view>
			<view class="bg-circle bg-circle-3"></view>
		</view>

		<!-- 英雄区域 -->
		<view class="hero-section">
			<view class="hero-content">
				<view class="hero-title">🎉 您的性格类型</view>
				<view class="personality-showcase">
					<view class="type-avatar">
						<view class="avatar-ring"></view>
						<view class="avatar-inner">
							<text class="type-code">{{ mbtiResult.type }}</text>
						</view>
						<view class="avatar-glow"></view>
					</view>
					<view class="type-info">
						<text class="type-name">{{ mbtiResult.typeName }}</text>
						<text class="type-subtitle">{{ getTypeCategory(mbtiResult.type) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 性格描述卡片 -->
		<view class="content-section">
			<view class="glass-card description-card">
				<view class="card-header">
					<view class="card-icon">✨</view>
					<text class="card-title">性格特质</text>
				</view>
				<view class="card-content">
					<text class="description-text">{{ mbtiResult.description }}</text>
				</view>
			</view>

			<!-- 性格倾向圆形进度条 -->
			<view class="glass-card tendencies-card">
				<view class="card-header">
					<view class="card-icon">📊</view>
					<text class="card-title">性格倾向分析</text>
				</view>
				<view class="tendencies-grid">
					<view class="tendency-circle" v-for="(item, index) in mbtiResult.tendencies" :key="index">
						<view class="circle-progress">
							<view class="circle-bg"></view>
							<view class="circle-fill" :style="getCircleStyle(item.percentage, item.color)"></view>
							<view class="circle-inner">
								<text class="circle-percentage">{{ item.percentage }}%</text>
								<text class="circle-label">{{ item.label }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 模式展示网格 -->
			<view class="modes-grid">
				<view class="mode-card leadership-card">
					<view class="mode-header">
						<view class="mode-emoji">👑</view>
						<text class="mode-title">领导模式</text>
					</view>
					<text class="mode-description">{{ mbtiResult.leadershipMode }}</text>
				</view>

				<view class="mode-card learning-card">
					<view class="mode-header">
						<view class="mode-emoji">🎓</view>
						<text class="mode-title">学习模式</text>
					</view>
					<text class="mode-description">{{ mbtiResult.learningMode }}</text>
				</view>

				<view class="mode-card problem-card">
					<view class="mode-header">
						<view class="mode-emoji">🧠</view>
						<text class="mode-title">解决问题</text>
					</view>
					<text class="mode-description">{{ mbtiResult.problemSolvingMode }}</text>
				</view>

				<view class="mode-card work-card">
					<view class="mode-header">
						<view class="mode-emoji">💼</view>
						<text class="mode-title">工作模式</text>
					</view>
					<text class="mode-description">{{ mbtiResult.workMode }}</text>
				</view>
			</view>

			<!-- 需要注意的方面 -->
			<view class="glass-card weakness-card">
				<view class="card-header">
					<view class="card-icon">⚠️</view>
					<text class="card-title">需要注意的方面</text>
				</view>
				<view class="weakness-grid">
					<view class="weakness-item" v-for="(weakness, index) in mbtiResult.weaknesses" :key="index">
						<view class="weakness-number">{{ index + 1 }}</view>
						<text class="weakness-text">{{ weakness }}</text>
					</view>
				</view>
			</view>

			<!-- 发展建议 -->
			<view class="glass-card development-card">
				<view class="card-header">
					<view class="card-icon">🚀</view>
					<text class="card-title">发展建议</text>
				</view>
				<view class="card-content">
					<text class="development-text">{{ mbtiResult.development }}</text>
				</view>
			</view>

			<!-- 适合领域标签云 -->
			<view class="glass-card fields-card">
				<view class="card-header">
					<view class="card-icon">🎯</view>
					<text class="card-title">适合的领域</text>
				</view>
				<view class="fields-cloud">
					<view class="field-bubble" v-for="(field, index) in mbtiResult.suitableFields" :key="index" :style="getFieldStyle(index)">
						<text class="field-text">{{ field }}</text>
					</view>
				</view>
			</view>

			<!-- 推荐职业 -->
			<view class="glass-card careers-card">
				<view class="card-header">
					<view class="card-icon">💎</view>
					<text class="card-title">推荐职业</text>
				</view>
				<view class="careers-list">
					<view class="career-item" v-for="(career, index) in mbtiResult.suitableCareers" :key="index">
						<view class="career-rank">{{ index + 1 }}</view>
						<text class="career-name">{{ career }}</text>
						<view class="career-star">⭐</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-actions">
			<view class="fab-button share-fab" @click="shareResult">
				<text class="fab-icon">📤</text>
				<text class="fab-text">分享</text>
			</view>
			<view class="fab-button retry-fab" @click="retakeTest">
				<text class="fab-icon">🔄</text>
				<text class="fab-text">重测</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { getMbtiData } from './mbtiData.js';

	export default {
		data() {
			return {
				mbtiResult: {
					type: 'ENFP',
					typeName: '竞选者',
					description: '正在加载您的测试结果...',
					tendencies: [],
					leadershipMode: '',
					learningMode: '',
					problemSolvingMode: '',
					workMode: '',
					weaknesses: [],
					development: '',
					suitableFields: [],
					suitableCareers: []
				}
			}
		},
		onLoad(options) {
			// 获取页面参数
			if (options && options.mbtiType) {
				this.getMbtiResultByType(options.mbtiType);
			}
		},
		mounted() {
			// 页面挂载完成
		},
		methods: {

			// 根据MBTI类型获取详细结果
			getMbtiResultByType(type) {
				console.log('获取MBTI类型结果:', type);

				// 从配置文件获取对应类型的详细信息
				const typeData = getMbtiData(type);
				this.mbtiResult = { ...typeData };

				// 可以在这里调用API获取更详细的个性化结果
				// this.$api.getMbtiDetailResult(type, res => {
				//     if (res.code === 200) {
				//         this.mbtiResult = res.data;
				//     }
				// });
			},

			// 获取类型分类
			getTypeCategory(type) {
				const categories = {
					'NT': '分析师 - 理性而独立的思想家',
					'NF': '外交官 - 富有想象力的理想主义者',
					'SJ': '守护者 - 实用而专注的合作者',
					'SP': '探索者 - 灵活而魅力十足的行动者'
				};

				const secondChar = type.charAt(1);
				const thirdChar = type.charAt(2);
				const key = secondChar + thirdChar;

				return categories[key] || '独特的性格类型';
			},

			// 获取圆形进度条样式
			getCircleStyle(percentage, color) {
				const rotation = (percentage / 100) * 360;
				return {
					'--progress-color': color,
					'--progress-rotation': rotation + 'deg'
				};
			},

			// 获取领域气泡样式
			getFieldStyle(index) {
				const colors = [
					'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
					'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
					'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
					'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
					'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
				];

				return {
					background: colors[index % colors.length],
					'animation-delay': (index * 0.1) + 's'
				};
			},

			// 分享结果
			shareResult() {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
					success: (res) => {
						switch(res.tapIndex) {
							case 0:
								this.shareToWeChat();
								break;
							case 1:
								this.shareToMoments();
								break;
							case 2:
								this.copyLink();
								break;
						}
					}
				});
			},

			// 分享到微信
			shareToWeChat() {
				uni.showToast({
					title: '分享到微信',
					icon: 'success'
				});
			},

			// 分享到朋友圈
			shareToMoments() {
				uni.showToast({
					title: '分享到朋友圈',
					icon: 'success'
				});
			},

			// 复制链接
			copyLink() {
				uni.setClipboardData({
					data: '我的MBTI测试结果：' + this.mbtiResult.type + ' - ' + this.mbtiResult.typeName,
					success: () => {
						uni.showToast({
							title: '已复制到剪贴板',
							icon: 'success'
						});
					}
				});
			},

			// 重新测试
			retakeTest() {
				uni.showModal({
					title: '确认重新测试',
					content: '重新测试将清除当前结果，是否继续？',
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/subPages/mbtiTest/index'
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
/* 全局样式 */
.analyze-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow-x: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 0;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
	width: 300rpx;
	height: 300rpx;
	top: 10%;
	right: -100rpx;
	animation-delay: 0s;
}

.bg-circle-2 {
	width: 200rpx;
	height: 200rpx;
	top: 60%;
	left: -50rpx;
	animation-delay: 2s;
}

.bg-circle-3 {
	width: 150rpx;
	height: 150rpx;
	top: 80%;
	right: 20%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(180deg); }
}

/* 英雄区域 */
.hero-section {
	position: relative;
	z-index: 1;
	padding: 80rpx 40rpx 60rpx;
	text-align: center;
}

.hero-content {
	animation: slideInDown 0.8s ease-out;
}

.hero-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 60rpx;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.personality-showcase {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.type-avatar {
	position: relative;
	margin-bottom: 40rpx;
	animation: pulse 2s ease-in-out infinite;
}

.avatar-ring {
	width: 240rpx;
	height: 240rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	position: absolute;
	top: -20rpx;
	left: -20rpx;
	animation: rotate 10s linear infinite;
}

.avatar-inner {
	width: 200rpx;
	height: 200rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10px);
}

.avatar-glow {
	position: absolute;
	width: 280rpx;
	height: 280rpx;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
	border-radius: 50%;
	top: -40rpx;
	left: -40rpx;
	animation: glow 3s ease-in-out infinite alternate;
}

.type-code {
	font-size: 64rpx;
	font-weight: 900;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.type-info {
	text-align: center;
}

.type-name {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 20rpx;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.type-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 500;
}

/* 内容区域 */
.content-section {
	position: relative;
	z-index: 1;
	padding: 0 30rpx 120rpx;
}

/* 玻璃卡片效果 */
.glass-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 30rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 0.6s ease-out;
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.card-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.card-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.card-content {
	line-height: 1.8;
}

.description-text {
	font-size: 32rpx;
	color: #666;
	text-align: justify;
}

/* 倾向性圆形进度条 */
.tendencies-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 40rpx;
	margin-top: 20rpx;
}

.tendency-circle {
	display: flex;
	justify-content: center;
}

.circle-progress {
	position: relative;
	width: 160rpx;
	height: 160rpx;
}

.circle-bg {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: #f0f0f0;
}

.circle-fill {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: conic-gradient(var(--progress-color) var(--progress-rotation), #f0f0f0 var(--progress-rotation));
	mask: radial-gradient(circle at center, transparent 60%, black 60%);
}

.circle-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}

.circle-percentage {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.circle-label {
	font-size: 22rpx;
	color: #666;
	margin-top: 5rpx;
	display: block;
}

/* 模式网格 */
.modes-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.mode-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 25rpx;
	padding: 30rpx;
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 0.6s ease-out;
}

.mode-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.mode-emoji {
	font-size: 40rpx;
	margin-right: 15rpx;
}

.mode-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.mode-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 特殊卡片颜色 */
.leadership-card { border-left: 8rpx solid #ff6b6b; }
.learning-card { border-left: 8rpx solid #4ecdc4; }
.problem-card { border-left: 8rpx solid #45b7d1; }
.work-card { border-left: 8rpx solid #96ceb4; }

/* 缺点网格 */
.weakness-grid {
	display: grid;
	gap: 25rpx;
}

.weakness-item {
	display: flex;
	align-items: flex-start;
	padding: 25rpx;
	background: linear-gradient(135deg, #fff5f5 0%, #ffe0e0 100%);
	border-radius: 20rpx;
	border-left: 6rpx solid #ff6b6b;
}

.weakness-number {
	width: 50rpx;
	height: 50rpx;
	background: #ff6b6b;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 24rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.weakness-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 发展建议 */
.development-text {
	font-size: 32rpx;
	color: #666;
	line-height: 1.8;
	text-align: justify;
}

/* 领域气泡云 */
.fields-cloud {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	justify-content: center;
}

.field-bubble {
	padding: 20rpx 35rpx;
	border-radius: 50rpx;
	color: white;
	font-weight: 600;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	animation: bubbleFloat 0.6s ease-out;
	transform: scale(0);
	animation-fill-mode: forwards;
}

.field-text {
	font-size: 28rpx;
}

/* 职业列表 */
.careers-list {
	display: grid;
	gap: 20rpx;
}

.career-item {
	display: flex;
	align-items: center;
	padding: 25rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
	border-radius: 20rpx;
	border-left: 6rpx solid #667eea;
	transition: transform 0.3s ease;
}

.career-item:hover {
	transform: translateX(10rpx);
}

.career-rank {
	width: 50rpx;
	height: 50rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 24rpx;
	margin-right: 20rpx;
}

.career-name {
	flex: 1;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.career-star {
	font-size: 32rpx;
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 40rpx;
	right: 40rpx;
	z-index: 100;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.fab-button {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border: 1px solid rgba(255, 255, 255, 0.3);
	transition: transform 0.3s ease;
}

.fab-button:active {
	transform: scale(0.95);
}

.fab-icon {
	font-size: 36rpx;
	margin-bottom: 5rpx;
}

.fab-text {
	font-size: 20rpx;
	color: #666;
	font-weight: 500;
}

/* 动画效果 */
@keyframes slideInDown {
	from {
		opacity: 0;
		transform: translateY(-100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.05); }
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes glow {
	from { opacity: 0.5; }
	to { opacity: 1; }
}

@keyframes bubbleFloat {
	from {
		opacity: 0;
		transform: scale(0) translateY(50rpx);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.tendencies-grid {
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.modes-grid {
		grid-template-columns: 1fr;
	}

	.floating-actions {
		flex-direction: row;
		bottom: 30rpx;
		right: 30rpx;
		left: 30rpx;
		justify-content: center;
	}

	.fab-button {
		width: 100rpx;
		height: 100rpx;
	}
}
</style>