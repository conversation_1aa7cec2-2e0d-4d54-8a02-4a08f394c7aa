<template>
	<view class="basic-dy-parse tn-safe-area-inset-bottom">
		<view class="tn-text-lg tn-text-bold book-name">
			【{{selBook}}】
		</view>
		<view class="list-warper">
			<tn-list-view :card="true" unlined="all">
			  <block v-for="(item,index) in listData" :key="index">
				<tn-list-cell @tap="doPlay(index)" :class="{'cur-active':index == audioActiveIndex}"  >{{item.lessonNum}}</tn-list-cell>
			  </block>
			</tn-list-view>
		</view>
		
		<view class="audio-warper">
			<le-audio
			    :activeIndex="audioActiveIndex"
			    :audioData="audioList"
			    :showAudioListIcon="showAudioListIcon"
			    :showAudioSpeedIcon="showAudioSpeedIcon"
			    :autoplay="autoplay"
			    @onOpenAudioList="onOpenAudioList"
			    @onAudioChange="onAudioChange"
			></le-audio>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Lession',
		components: {},
		data() {
			return {
				dyVidwoUrl: "",
				videoResult: {
					video: "",
					image: "",
					title: ""
				},
				listData:[],
				audioActiveIndex:null,
				audioList:[],
				showAudioListIcon:false,
				showAudioSpeedIcon:true,
				autoplay:false,
				selBook:"",
			}
		},
		onLoad(options) {
			console.log("onload....lesson...")
			this.selBook =options.name
			this.stopPlay()
			this.getAllLession()
		},
		onHide() {
			console.log("onhide....lesson...")
			this.audioList=[]
		},
		onShow(options) {
			console.log("onshow....lesson...")
			this.stopPlay()
			this.getAllLession()
		},
		methods: {
			getAllLession(){
				let param={
					groupName:this.selBook,
				}
				this.$api.getLessonData(param,(res)=>{
					
					if (res.code == 200) {
						this.audioList=[]
						this.listData = res.data
						this.listData.forEach(e=>{
							this.audioList.push({
								title:e.lessonNum,
								fileUrl:e.audioUrl,
							})
						})
					} else {
						this.$common.errorToShow(res.msg)
					}
				})
			},
			onOpenAudioList(){
				
			},
			onAudioChange(audio,index){
				this.audioActiveIndex = index
			},
			doPlay(index){
				console.log("------",index)
				this.audioActiveIndex=index
			},
			stopPlay(){
				this.audioList=[]
			}
		}
	}
</script>

<style lang="scss" scoped>
	.basic-dy-parse {
		background-color: $tn-bg-gray-color;
		height: 100vh;
		position: relative;
	}
	.book-name{
		padding-left: 30upx;
		padding-top:10upx;
		font-size: 28upx;
	}
	.banner-top{
		box-sizing: border-box;
		padding-top: 30upx;
	}
	.list-warper{
		box-sizing: border-box;
		padding-top: 10upx;
		height: calc( 100% - 370upx );
		overflow: scroll;
	}
	.audio-warper{
		position: absolute;
		bottom: 0;
		box-sizing: border-box;
		padding: 20rpx 0upx 10upx 0 ;
		border-radius: 10upx 10upx 0 0;
		background: #000;
	}
	.cur-active{
		background: #fff;
	}
</style>