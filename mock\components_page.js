/**
 * 页面展示列表数据
 */
export default {
  data: [
    {
      title: '基础组件',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'signpost',
          title: '列表',
          url: '/componentsPage/list/list'
        },
        {
          icon: 'circle-arrow',
          title: 'Loading加载',
          url: '/componentsPage/loading/loading'
        },
        {
          icon: 'tag',
          title: 'tabs标签',
          url: '/componentsPage/tabs/tabs'
        },
        {
          icon: 'receipt',
          title: 'sticky吸顶',
          url: '/componentsPage/sticky/sticky'
        },
        {
          icon: 'all',
          title: 'navBar导航栏',
          url: '/componentsPage/nav-bar/nav-bar'
        },
        {
          icon: 'sound',
          title: 'noticeBar通知栏',
          url: '/componentsPage/notice-bar/notice-bar'
        },
        {
          icon: 'image',
          title: 'swiper轮播图',
          url: '/componentsPage/swiper/swiper'
        },
        {
          icon: 'server',
          title: 'collapse折叠面板',
          url: '/componentsPage/collapse/collapse'
        },
        {
          icon: 'more-circle',
          title: 'readMore查看更多',
          url: '/componentsPage/read-more/read-more'
        },
        {
          icon: 'success-square',
          title: 'steps步骤条',
          url: '/componentsPage/steps/steps'
        },
        {
          icon: 'clock',
          title: 'timeLine时间轴',
          url: '/componentsPage/time-line/time-line'
        },
        {
          icon: 'level',
          title: 'indexList索引列表',
          url: '/componentsPage/index-list/index-list'
        },
        {
          icon: 'group-square',
          title: 'scrollList横向滚动',
          url: '/componentsPage/scroll-list/scroll-list'
        },
        {
          icon: 'brand',
          title: 'swipeAction滑动菜单',
          url: '/componentsPage/swipe-action/swipe-action'
        },
        {
          icon: 'pharmacy',
          title: 'fab悬浮按钮',
          url: '/componentsPage/fab/fab'
        },
        {
          icon: 'cardbag',
          title: '懒加载',
          url: '/componentsPage/lazy-load/lazy-load'
        },
        {
          icon: 'down-double',
          title: '加载更多',
          url: '/componentsPage/load-more/load-more'
        },
        {
          icon: 'menu-more',
          title: '骨架屏',
          url: '/componentsPage/skeleton/skeleton'
        },
        {
          icon: 'empty-search',
          title: '空页面',
          url: '/componentsPage/empty/empty'
        },
        {
          icon: 'share',
          title: '商品导航',
          url: '/componentsPage/goods-nav/goods-nav'
        }
      ]
    },
    {
      title: '弹框组件',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'prize',
          title: '弹出层',
          url: '/componentsPage/popup/popup'
        },
        {
          icon: 'bankcard',
          title: '模态框',
          url: '/componentsPage/modal/modal'
        },
        {
          icon: 'comment',
          title: 'Toast',
          url: '/componentsPage/toast/toast'
        },
        {
          icon: 'creative',
          title: '提示信息框',
          url: '/componentsPage/tips/tips'
        },
        {
          icon: 'reply',
          title: '压屏窗',
          url: '/componentsPage/landscape/landscape'
        }
      ]
    },
    {
      title: '表单组件',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'image-text',
          title: 'Form表单',
          url: '/componentsPage/form/form'
        },
        {
          icon: 'square',
          title: 'Input输入框',
          url: '/componentsPage/input/input'
        },
        {
          icon: 'circle-fill',
          title: 'Radio单选框',
          url: '/componentsPage/radio/radio'
        },
        {
          icon: 'copy',
          title: 'Checkbox复选框',
          url: '/componentsPage/check-box/check-box'
        },
        {
          icon: 'ticket',
          title: 'ActionSheet操作菜单',
          url: '/componentsPage/action-sheet/action-sheet'
        },
        {
          icon: 'deploy',
          title: 'Picker选择器',
          url: '/componentsPage/picker/picker'
        },
        {
          icon: 'organizatio',
          title: 'Select列选择器',
          url: '/componentsPage/select/select'
        },
        {
          icon: 'data',
          title: '验证码倒计时',
          url: '/componentsPage/verification-code/verification-code'
        },
        {
          icon: 'power',
          title: 'Switch开关',
          url: '/componentsPage/switch/switch'
        },
        {
          icon: 'upload',
          title: '图片上传',
          url: '/componentsPage/image-upload/image-upload'
        },
        {
          icon: 'calendar',
          title: '日历',
          url: '/componentsPage/calendar/calendar'
        },
        {
          icon: 'edit-write',
          title: '签名板',
          url: '/componentsPage/sign-board/sign-board'
        },
        {
          icon: 'safe',
          title: '验证码输入',
          url: '/componentsPage/verification-code-input/verification-code-input'
        },
        {
          icon: 'expand',
          title: '分段器',
          url: '/componentsPage/subsection/subsection'
        }
      ]
    },
    {
      title: '数据组件',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'watercup',
          title: 'Progress进度条',
          url: '/componentsPage/progress/progress'
        },
        {
          icon: 'star',
          title: 'rate评分',
          url: '/componentsPage/rate/rate'
        },
        {
          icon: 'light',
          title: 'slider滑动条',
          url: '/componentsPage/slider/slider'
        },
        {
          icon: 'statistics',
          title: 'numberBox步进输入',
          url: '/componentsPage/number-box/number-box'
        },
        {
          icon: 'keyboard-circle',
          title: 'keyboard键盘',
          url: '/componentsPage/keyboard/keyboard'
        },
        {
          icon: 'time',
          title: 'countDown倒计时',
          url: '/componentsPage/count-down/count-down'
        },
        {
          icon: 'group-circle',
          title: 'countTo数字跳转',
          url: '/componentsPage/count-to/count-to'
        },
        {
          icon: 'sequence-vertical',
          title: 'countScroll数字滚动',
          url: '/componentsPage/count-scroll/count-scroll'
        }
      ]
    }
  ]
}