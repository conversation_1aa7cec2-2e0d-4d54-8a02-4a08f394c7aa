<template>
	<view class="user">
		<view class="user-content">
			<view class="user-panel">
				<view class="iconfont icon-user" style="font-size: 100upx;" />
				<view class="username">
					{{ userInfo.userName }}({{userInfo.nickName}})
				</view>
			</view>
			<view class="user-body">
<!-- 				<uni-list>
					<uni-list-item v-for="item in itemList" :key="item.id" :title="item.title" :arrow="item.arrow" :extra-text="item.ext"
					 @click="listItemClick(item.page)" />
				</uni-list> -->
			</view>
			<view class="user-login-out">
				<button @click="doLogout">
					退出登录
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	// 按需引入, 更小的应用体积
	import "./index.scss";
	export default {
		name: "User",
		components: {
		},
		data() {
			return {
				current1: 3,
				userName: "12345678",
				userInfo:{userName:"",},
				itemList: [{
						id: 1,
						title: "购买记录",
						page: '/pages/user/payRecord/index',
						ext: "",
						arrow: "right"
					},
					{
						id: 2,
						title: "个人信息",
						page: '/pages/user/userinfo/index',
						ext: "",
						arrow: "right"
					},
					{
						id: 3,
						title: "修改密码",
						page: '/pages/user/changePwd/index',
						ext: "",
						arrow: "right"
					},
					{
						id: 4,
						title: "版本信息",
						page: '/pages/user/systeminfo/index',
						ext: "V1.2.201",
						arrow: ""
					},
					{
						id: 5,
						title: "推送设置",
						page: '/pages/user/setting/index',
						ext: "",
						arrow: "right"
					}
				],
			};
		},
		onLoad() {
			this.userInfo=this.$db.get("userInfo")
		},
		methods: {
			onClick(value) {
				uni.redirectTo({
					url: this.tabList1[value].tag
				})
			},
			listItemClick(page) {
				console.log(page)
				this.$common.navigateTo(page)
			},
			doLogout() {
				console.log("登出")	
				this.$db.clear();
				uni.redirectTo({
					url: '/pages/login/login/index1'
				})
			}
		},
	};
</script>
