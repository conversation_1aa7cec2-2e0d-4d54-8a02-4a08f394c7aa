<template>
	<view class="answer-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="bg-wave bg-wave-1"></view>
			<view class="bg-wave bg-wave-2"></view>
			<view class="bg-wave bg-wave-3"></view>
		</view>

		<!-- 顶部进度区域 -->
		<view class="progress-section">
			<view class="progress-header">
				<view class="progress-info">
					<text class="current-question">第 {{ currentTopicNumber }} 题</text>
					<text class="total-questions">共 {{ questionCount }} 题</text>
				</view>
				<view class="progress-percentage">{{ Math.round((currentTopicNumber / questionCount) * 100) }}%</view>
			</view>
			<view class="progress-bar">
				<view class="progress-fill" :style="{ width: (currentTopicNumber / questionCount) * 100 + '%' }"></view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 问题卡片 -->
			<view class="question-card">
				<view class="question-header">
					<view class="question-icon">🤔</view>
					<text class="question-label">问题</text>
				</view>
				<view class="question-content">
					<text class="question-text">{{ currentTopic.questionDesc }}</text>
				</view>
			</view>

			<!-- 选项区域 -->
			<view class="options-section">
				<view class="options-title">请选择最符合你的选项：</view>
				<view class="options-container">
					<view
						class="option-card option-a"
						:class="{ 'selected': selected == 'A' }"
						@click="selecteHandle('A')"
					>
						<view class="option-header">
							<view class="option-letter">A</view>
							<view class="option-check" v-if="selected == 'A'">✓</view>
						</view>
						<text class="option-text">{{ currentTopic.options?.A?.[0] || '选项A' }}</text>
					</view>

					<view
						class="option-card option-b"
						:class="{ 'selected': selected == 'B' }"
						@click="selecteHandle('B')"
					>
						<view class="option-header">
							<view class="option-letter">B</view>
							<view class="option-check" v-if="selected == 'B'">✓</view>
						</view>
						<text class="option-text">{{ currentTopic.options?.B?.[0] || '选项B' }}</text>
					</view>
				</view>
			</view>

			<!-- 下一步按钮 -->
			<view class="next-button-container">
				<view class="next-button" :class="{ 'disabled': !selected }" @click="nextHandle">
					<text class="next-text">{{ btnText }}</text>
					<view class="next-icon">{{ btnText === '提交' ? '🎯' : '→' }}</view>
				</view>
			</view>
		</view>

		<!-- 广告位区域 -->
		<view class="ad-section">
			<view class="ad-container">
				<view class="ad-header">
					<text class="ad-label">广告</text>
				</view>
				<view class="ad-content">
					<view class="ad-placeholder">
						<view class="ad-icon">📢</view>
						<text class="ad-text">广告位招租</text>
						<text class="ad-desc">在这里展示您的广告内容</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 测试按钮（开发用） -->
		<view class="dev-test" @click="testAnalyze('INFP')" v-if="false">
			测试INFP
		</view>
	</view>
</template>


<script>
	export default {
		data() {
			return {
				currentTopicNumber:1, // 当前的题号
				topics:[], // 题目列表
				currentTopic:{
					
				} ,// 当前题目数据
				selected:"", // 选择了哪一个
				answers:{
					"E":0,"I":0,
					"N":0,"S":0,
					"F":0,"T":0,
					"J":0,"P":0
				} , // 选择的答案列表
				btnText:"下一题" ,// 按钮文本 “下一题”或“提交”
				questionCount:0,
				startTime:"",
			}
		},
		mounted() {
			// 逐行逐字显示提示
			this.initAnswers();
			this.getTopics();
			this.startTime = new Date().Format('yyyy-MM-dd hh:mm:ss')
		},
		methods: {
			
			initAnswers(){
				this.answers = {
					"E":0,"I":0,
					"N":0,"S":0,
					"F":0,"T":0,
					"J":0,"P":0
				}
			},
			
			async getTopics() {
				let openId = uni.getStorageSync("openid")
				this.$api.getMbtiTops(openId,res=>{
					console.log(res)
					if (res.code == 200) {
						
						// 处理下 options
						this.topics = res.data;
						this.topics.map(item=>{
							item.options=JSON.parse(item.options);
							return item							
						})
						
						
						// 显示第一题
						this.currentTopic = this.topics[this.currentTopicNumber - 1];
						this.questionCount = this.topics.length;
					}
				})
			},
			
			// 选择题目处理
			selecteHandle(selectOp) {
				this.selected = selectOp;
			},
			
			// 下一题
			nextHandle() {
				if (this.currentTopicNumber == this.questionCount) {
					// 执行提交，统计选项推算出性格类型
					this.commitHandle();
					return 1;
				}
			
				if (this.selected == '') {
					// 空选择提示
					uni.showToast({
						icon: 'none',
						title: '请先选择'
					});
					return 1;
				}
			
				// 存储选择, 存储到数组中，后面统计在分拣
				// 获取分类
				let option = this.currentTopic.options
				let selectType = option[this.selected][1]
				
				this.answers[selectType]++;
			
				// 题号加 1，更换题目
				this.currentTopicNumber++;
				this.currentTopic = this.topics[this.currentTopicNumber - 1];
				
				// 如果显示了最后一题，修改按钮文本为“提交”
				if(this.currentTopicNumber == this.questionCount){
					this.btnText = "提交";
				}
				// 选择置空
				this.selected = '';
			},
			commitHandle(){
				// 分析结果
				let result = [];
				if (this.answers.E>this.answers.I) {
					result.push("E")
				}else{
					result.push("I")
				}
				if (this.answers.N>this.answers.S) {
					result.push("N")
				}else{
					result.push("S")
				}
				if (this.answers.F>this.answers.T) {
					result.push("F")
				}else{
					result.push("T")
				}
				
				if (this.answers.J>this.answers.P) {
					result.push("J")
				}else{
					result.push("P")
				}
				
				let openId = uni.getStorageSync("openid")
				let data = {
					username:openId,
					mbtiKind:result.join(""),
					startTime:this.startTime,
				}
				
				this.$api.addMbtiResult(data,res=>{
					if (res.code == 200){
						// 去结果页面
						uni.redirectTo({
							url: `/subPages/mbtiTest/analyze?mbtiType=${result.join("")}`
						});
					}
				})
				
				
				
				
			},
			testAnalyze(mbtiKind){
				uni.redirectTo({
					url: `/subPages/mbtiTest/analyze?mbtiType=${mbtiKind}`
				});
			}
		}
	}
</script>

<style scoped>
/* 全局容器 */
.answer-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow-x: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 0;
}

.bg-wave {
	position: absolute;
	width: 200%;
	height: 200rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50%;
	animation: wave 8s ease-in-out infinite;
}

.bg-wave-1 {
	top: 20%;
	left: -50%;
	animation-delay: 0s;
}

.bg-wave-2 {
	top: 60%;
	right: -50%;
	animation-delay: 2s;
}

.bg-wave-3 {
	top: 80%;
	left: -30%;
	animation-delay: 4s;
}

@keyframes wave {
	0%, 100% { transform: translateX(0) rotate(0deg); opacity: 0.3; }
	50% { transform: translateX(100rpx) rotate(180deg); opacity: 0.6; }
}

/* 进度区域 */
.progress-section {
	position: relative;
	z-index: 1;
	padding: 60rpx 40rpx 40rpx;
	animation: slideInDown 0.6s ease-out;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.progress-info {
	display: flex;
	flex-direction: column;
}

.current-question {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 10rpx;
}

.total-questions {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.progress-percentage {
	font-size: 48rpx;
	font-weight: 900;
	color: white;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.progress-bar {
	width: 100%;
	height: 12rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 6rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #ff6b6b 0%, #ffa500 100%);
	border-radius: 6rpx;
	transition: width 0.5s ease;
	box-shadow: 0 0 20rpx rgba(255, 107, 107, 0.5);
}

/* 主内容区域 */
.main-content {
	position: relative;
	z-index: 1;
	padding: 0 40rpx;
}

/* 问题卡片 */
.question-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 30rpx;
	padding: 50rpx;
	margin-bottom: 50rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 0.6s ease-out;
}

.question-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.question-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.question-label {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.question-content {
	padding: 30rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
	border-radius: 20rpx;
	border-left: 6rpx solid #667eea;
}

.question-text {
	font-size: 32rpx;
	color: #333;
	line-height: 1.8;
	font-weight: 500;
}

/* 选项区域 */
.options-section {
	margin-bottom: 60rpx;
	animation: slideInUp 0.8s ease-out;
}

.options-title {
	font-size: 28rpx;
	color: white;
	margin-bottom: 40rpx;
	text-align: center;
	font-weight: 500;
}

.options-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.option-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 25rpx;
	padding: 40rpx;
	border: 3rpx solid transparent;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.option-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.1) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.option-card:hover::before {
	opacity: 1;
}

.option-card.selected {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	transform: translateY(-5rpx);
	box-shadow: 0 25rpx 70rpx rgba(102, 126, 234, 0.3);
}

.option-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.option-letter {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 28rpx;
}

.option-check {
	width: 50rpx;
	height: 50rpx;
	background: #4CAF50;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	animation: checkBounce 0.3s ease;
}

.option-text {
	font-size: 30rpx;
	color: #333;
	line-height: 1.6;
	font-weight: 500;
}

/* 下一步按钮 */
.next-button-container {
	text-align: center;
	margin-bottom: 60rpx;
	animation: slideInUp 1s ease-out;
}

.next-button {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
	border-radius: 50rpx;
	padding: 25rpx 60rpx;
	box-shadow: 0 20rpx 60rpx rgba(255, 107, 107, 0.4);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.next-button:not(.disabled):active {
	transform: scale(0.95);
}

.next-button.disabled {
	background: #ccc;
	box-shadow: none;
	cursor: not-allowed;
}

.next-text {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
	margin-right: 15rpx;
}

.next-icon {
	font-size: 28rpx;
	color: white;
}

/* 广告位区域 */
.ad-section {
	position: relative;
	z-index: 1;
	padding: 0 40rpx 60rpx;
}

.ad-container {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 25rpx;
	overflow: hidden;
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: slideInUp 1.2s ease-out;
}

.ad-header {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	padding: 20rpx 30rpx;
	text-align: center;
}

.ad-label {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.ad-content {
	padding: 60rpx;
	min-height: 300rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.ad-placeholder {
	text-align: center;
	opacity: 0.6;
}

.ad-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	display: block;
}

.ad-text {
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
	margin-bottom: 15rpx;
	display: block;
}

.ad-desc {
	font-size: 24rpx;
	color: #999;
	display: block;
}

/* 开发测试按钮 */
.dev-test {
	position: fixed;
	bottom: 20rpx;
	right: 20rpx;
	background: #ff4444;
	color: white;
	padding: 20rpx;
	border-radius: 10rpx;
	font-size: 24rpx;
	z-index: 1000;
}

/* 动画效果 */
@keyframes slideInDown {
	from {
		opacity: 0;
		transform: translateY(-100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(100rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes checkBounce {
	0% { transform: scale(0); }
	50% { transform: scale(1.2); }
	100% { transform: scale(1); }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.progress-section {
		padding: 40rpx 30rpx 30rpx;
	}

	.main-content {
		padding: 0 30rpx;
	}

	.question-card {
		padding: 40rpx;
	}

	.option-card {
		padding: 30rpx;
	}

	.ad-section {
		padding: 0 30rpx 60rpx;
	}

	.ad-content {
		padding: 40rpx;
		min-height: 250rpx;
	}
}
</style>