<template>
	<view id="answer_container">
		<uni-card class="uni_card" :title="'第' + currentTopicNumber + '题'" :extra="'剩' + (questionCount - currentTopicNumber) + '题'">
			<!-- 题目 -->
			<!-- <view class="kindType">{{ currentTopic.kindDesc }}</view> -->
			<view class="question">Q：{{ currentTopic.questionDesc }}</view>
			<!-- 选择区 -->
			<view class="options">
				<view class="optionA option" @click="selecteHandle('A')" :class="{ selected: selected == 'A' }">A：{{ currentTopic.options.A[0] }}</view>
				<view class="optionB option" @click="selecteHandle('B')" :class="{ selected: selected == 'B' }">B: {{ currentTopic.options.B[0] }}</view>
			</view>
			<!-- 下一步按钮 -->
			<view class="next_btn" @click="nextHandle">{{ btnText }}</view>
			<view style="height: 10px"></view>
		</uni-card>
	</view>
</template>


<script>
	export default {
		data() {
			return {
				currentTopicNumber:1, // 当前的题号
				topics:[], // 题目列表
				currentTopic:{
					
				} ,// 当前题目数据
				selected:"", // 选择了哪一个
				answers:{
					"E":0,"I":0,
					"N":0,"S":0,
					"F":0,"T":0,
					"J":0,"P":0
				} , // 选择的答案列表
				btnText:"下一题" ,// 按钮文本 “下一题”或“提交”
				questionCount:0,
				startTime:"",
			}
		},
		mounted() {
			// 逐行逐字显示提示
			this.initAnswers();
			this.getTopics();
			this.startTime = new Date().Format('yyyy-MM-dd hh:mm:ss')
		},
		methods: {
			
			initAnswers(){
				this.answers = {
					"E":0,"I":0,
					"N":0,"S":0,
					"F":0,"T":0,
					"J":0,"P":0
				}
			},
			
			async getTopics() {
				let openId = uni.getStorageSync("openid")
				this.$api.getMbtiTops(openId,res=>{
					console.log(res)
					if (res.code == 200) {
						
						// 处理下 options
						this.topics = res.data;
						this.topics.map(item=>{
							item.options=JSON.parse(item.options);
							return item							
						})
						
						
						// 显示第一题
						this.currentTopic = this.topics[this.currentTopicNumber - 1];
						this.questionCount = this.topics.length;
					}
				})
			},
			
			// 选择题目处理
			selecteHandle(selectOp) {
				this.selected = selectOp;
			},
			
			// 下一题
			nextHandle() {
				if (this.currentTopicNumber == this.questionCount) {
					// 执行提交，统计选项推算出性格类型
					commitHandle();
					return 1;
				}
			
				if (this.selected == '') {
					// 空选择提示
					uni.showToast({
						icon: 'none',
						title: '请先选择'
					});
					return 1;
				}
			
				// 存储选择, 存储到数组中，后面统计在分拣
				// 获取分类
				let option = this.currentTopic.options
				let selectType = option[this.selected][1]
				
				this.answers[selectType]++;
			
				// 题号加 1，更换题目
				this.currentTopicNumber++;
				this.currentTopic = this.topics[this.currentTopicNumber - 1];
				
				// 如果显示了最后一题，修改按钮文本为“提交”
				if(this.currentTopicNumber == this.questionCount){
					this.btnText = "提交";
				}
				// 选择置空
				this.selected = '';
			},
			commitHandle(){
				// 分析结果
				let result = [];
				if (this.answers.E>this.answers.I) {
					result.push("E")
				}else{
					result.push("I")
				}
				if (this.answers.N>this.answers.S) {
					result.push("N")
				}else{
					result.push("S")
				}
				if (this.answers.F>this.answers.T) {
					result.push("F")
				}else{
					result.push("T")
				}
				
				if (this.answers.J>this.answers.P) {
					result.push("J")
				}else{
					result.push("P")
				}
				
				let openId = uni.getStorageSync("openid")
				let data = {
					username:openId,
					mbtiKind:result.join(""),
					startTime:this.startTime,
				}
				
				this.$api.addMbtiResult(data,res=>{
					if (res.code == 200){
						// 去结果页面
						
						
					}
				})
				
				
				
				
			}
		}
	}
</script>

<style scoped>
.question {
	font-size: 16px;
	font-weight: 600;
}
.kindType{
	margin-top: 10px;
}
.options {
	margin-top: 15px;
}

.option {
	padding: 10px 10px;
	margin-top: 10px;
}

.next_btn {
	width: max-content;
	margin: 0 auto;
	border: solid #698bca 1px;
	margin-top: 12px;
	padding: 8px 22px;
	border-radius: 10px;
}

.selected {
	background-color: #e9e7e7;
	border-radius: 10px;
	color: #000;
}
</style>