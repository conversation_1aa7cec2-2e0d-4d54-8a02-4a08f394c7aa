/**
 * 页面展示列表数据
 */
export default {
  data: [
    {
      title: '登录注册',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'send',
          title: '火箭登录',
          author: '图鸟北北',
          url: '/templatePage/login/demo1/demo1'
        },
        {
          icon: 'send',
          title: '粒子登录',
          author: '图鸟北北',
          url: '/templatePage/login/demo2/demo2'
        },
        {
          icon: 'send',
          title: '背景登录',
          author: '图鸟北北',
          url: '/templatePage/login/demo3/demo3'
        },
        {
          icon: 'send',
          title: '简约登录',
          author: '图鸟北北',
          url: '/templatePage/login/demo4/demo4'
        }
      ]
    },
    {
      title: '充值提现',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'send',
          title: '蓝色充值',
          author: '图鸟北北',
          url: '/templatePage/money/demo1/demo1'
        },
        {
          icon: 'send',
          title: '绿色充值',
          author: '图鸟北北',
          url: '/templatePage/money/demo2/demo2'
        },
        {
          icon: 'send',
          title: '暗黑充值',
          author: '图鸟北北',
          url: '/templatePage/money/demo3/demo3'
        }
      ]
    },
    {
      title: '个人中心',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'send',
          title: '夏天个人',
          author: '图鸟北北',
          url: '/templatePage/my/demo1/demo1'
        },
        {
          icon: 'send',
          title: '图鸟个人',
          author: '图鸟北北',
          url: '/templatePage/my/demo2/demo2'
        },
        {
          icon: 'send',
          title: '外卖个人',
          author: '图鸟北北',
          url: '/templatePage/my/demo3/demo3'
        },
        {
          icon: 'send',
          title: '高端个人',
          author: '图鸟北北',
          url: '/templatePage/my/demo4/demo4'
        }
      ]
    },
    {
      title: '常用首页',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'send',
          title: '音乐首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/music'
        },
        {
          icon: 'send',
          title: '课程首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/course'
        },
        {
          icon: 'send',
          title: '设计首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/design'
        },
        {
          icon: 'send',
          title: '招聘首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/job'
        },
        {
          icon: 'send',
          title: '投屏首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/screen'
        },
        {
          icon: 'send',
          title: '壁纸首页',
          author: '图鸟北北',
          url: '/templatePage/home/<USER>/wallpaper'
        },
      ]
    },
    {
      title: '骚气页面',
      backgroundColor: 'tn-cool-bg-color-1',
      list: [
        {
          icon: 'send',
          title: '健康码',
          author: '图鸟北北',
          url: '/templatePage/health/qrcode/qrcode'
        },
        {
          icon: 'send',
          title: '关于我们',
          author: 'Jaylen',
          url: '/templatePage/life/about/about'
        },
        {
          icon: 'send',
          title: '全新出发',
          author: '你的小可爱',
          url: '/templatePage/life/outset/outset'
        },
        {
          icon: 'send',
          title: '资讯左图',
          author: '图鸟北北',
          url: '/templatePage/article/demo1/demo1'
        },
        {
          icon: 'send',
          title: '资讯右图',
          author: '图鸟北北',
          url: '/templatePage/article/demo2/demo2'
        },
        {
          icon: 'send',
          title: '全屏轮播',
          author: '图鸟北北',
          url: '/templatePage/life/fullpage/fullpage'
        },
        {
          icon: 'rocket',
          title: '浏览器',
          author: '图鸟北北',
          url: '/templatePage/life/browser/browser'
        },
        {
          icon: 'rocket',
          title: '时钟',
          author: '图鸟北北',
          url: '/templatePage/time/clock/clock'
        }
      ]
    },
    {
       title: '动效元素',
       backgroundColor: 'tn-cool-bg-color-1',
       list: [
         {
           icon: 'send',
           title: '加载动画',
           author: '图鸟北北',
           url: '/templatePage/animate/loading/loading'
         },
         {
           icon: 'send',
           title: '流星悬浮',
           author: '图鸟北北',
           url: '/templatePage/animate/suspended/suspended'
         },
         {
           icon: 'send',
           title: '随机粒子',
           author: 'Jaylen',
           url: '/templatePage/animate/particle/particle'
         },
         {
           icon: 'send',
           title: '相册图集',
           author: '你的小可爱',
           url: '/templatePage/animate/photo/photo'
         },
         {
           icon: 'send',
           title: '镂空效果',
           author: '你的小可爱',
           url: '/templatePage/animate/hollow/hollow'
         },
         {
           icon: 'send',
           title: '泡泡飘出',
           author: 'Jaylen',
           url: '/templatePage/animate/bubble/bubble'
         },
         {
           icon: 'send',
           title: 'css波浪',
           author: '图鸟北北',
           url: '/templatePage/animate/wave/wave'
         }
       ]
     },
     {
        title: '群友力献',
        backgroundColor: 'tn-cool-bg-color-1',
        list: [
          {
            icon: 'send',
            title: '3D全景(第三方，有免费版付费版)',
            author: '图鸟北北 & 芊云全景',
            url: '/templatePage/life/pano/pano'
          },
          {
            icon: 'send',
            title: '3D模型(第三方，有免费版付费版)',
            author: '图鸟北北 & 芊云全景',
            url: '/templatePage/life/pano/model'
          },
          {
            icon: 'send',
            title: '微信红包封面',
            author: '微信红包封面',
            url: '/templatePage/life/cover/cover'
          },
          {
            icon: 'send',
            title: '营销小游戏-魔方',
            author: '最帅的你',
            url: '/templatePage/life/cube/cube'
          },
          {
            icon: 'rocket',
            title: '图鸟图表(待开源，免费开源)',
            author: '图鸟北北 & Ucharts秋云',
            url: '/templatePage/life/candle/candle'
          },
          {
            icon: 'rocket',
            title: '图鸟生态，期待你的加入',
            author: '合作微信 tnkewo',
            url: '/templatePage/life/candle/candle'
          }
        ]
      }
  ]
}