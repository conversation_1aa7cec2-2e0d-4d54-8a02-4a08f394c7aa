<script>
	
	export default {
		onLaunch() {
			this.getLoginCode()
		},
		onShow: function() {
			// this.$api.getPersonInfo({},(res)=>{
			// 	console.log("refrash")
			// });
		},
		onHide: function() {
			//console.log('App Hide')
		},
		methods: {
			getLoginCode() {
				let that = this
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						let code = loginRes.code;
						console.log(loginRes)
						that.$api.touristLogin({code: code},res => {
							console.log(res)
							if (res.code == 200) {
								uni.setStorageSync("openid",res.data.openId)
							}
						})
					},
				});
			},
		}
	}
</script>
<style lang="scss">
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  @import './tuniao-ui/index.scss';
  @import './tuniao-ui/iconfont.css';
</style>