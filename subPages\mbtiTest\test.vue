<template>
	<view class="test-container">
		<view class="test-title">MBTI测试结果预览</view>
		
		<view class="test-buttons">
			<view class="test-btn" v-for="type in mbtiTypes" :key="type" @click="previewResult(type)">
				{{ type }}
			</view>
		</view>
		
		<view class="test-note">
			点击上方按钮可以预览不同MBTI类型的结果展示页面
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mbtiTypes: [
					'ENFP', 'INFP', 'ENFJ', 'INFJ',
					'ENTP', 'INTP', 'ENTJ', 'INTJ',
					'ESTJ', 'ISTJ', 'ESFJ', 'ISFJ',
					'ESTP', 'ISTP', 'ESFP', 'ISFP'
				]
			}
		},
		methods: {
			previewResult(type) {
				uni.navigateTo({
					url: `/subPages/mbtiTest/analyze?mbtiType=${type}`
				});
			}
		}
	}
</script>

<style scoped>
.test-container {
	padding: 40rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.test-title {
	text-align: center;
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 60rpx;
}

.test-buttons {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
	margin-bottom: 60rpx;
}

.test-btn {
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.test-note {
	text-align: center;
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}
</style>
